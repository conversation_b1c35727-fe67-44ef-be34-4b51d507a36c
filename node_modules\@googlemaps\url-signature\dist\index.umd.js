!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("crypto-js/enc-base64"),require("crypto-js/hmac-sha1")):"function"==typeof define&&define.amd?define(["exports","crypto-js/enc-base64","crypto-js/hmac-sha1"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).urlSignature={},e.Base64,e.HmacSHA1)}(this,(function(e,t,n){"use strict";function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=r(t),o=r(n);function c(e,t){var n=function(e){var t=e.replace(/-/g,"+").replace(/_/g,"/");return a.default.parse(t)}(t);return o.default(e,n).toString(a.default).replace(/\+/g,"-").replace(/\//g,"_")}function u(e,t){return"string"==typeof e&&(e=new URL(e)),c("".concat(e.pathname).concat(e.search),t)}e.createSignature=u,e.createSignatureForPathAndQuery=c,e.signUrl=function(e,t){return"string"==typeof e&&(e=new URL(e)),new URL(e.toString()+"&signature="+u(e,t))},Object.defineProperty(e,"__esModule",{value:!0})}));
