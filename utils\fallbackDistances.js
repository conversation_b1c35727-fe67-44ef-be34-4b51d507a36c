/**
 * Fallback distance calculations for Norwegian locations when Google Maps API is unavailable
 * These are approximate driving distances and times from Oslo
 */

const norwegianDistances = {
  // Southern Norway
  'Kristiansand': { distance: '320 km', duration: '4 hours 30 mins' },
  'Stavanger': { distance: '520 km', duration: '6 hours 30 mins' },
  'Bergen': { distance: '460 km', duration: '7 hours' },
  'Haugesund': { distance: '480 km', duration: '6 hours' },
  
  // Eastern Norway
  'Lillehammer': { distance: '180 km', duration: '2 hours 15 mins' },
  'Hamar': { distance: '130 km', duration: '1 hour 30 mins' },
  'Kongsvinger': { distance: '100 km', duration: '1 hour 15 mins' },
  'Elverum': { distance: '150 km', duration: '1 hour 45 mins' },
  'Gjøvik': { distance: '120 km', duration: '1 hour 30 mins' },
  
  // Western Norway
  'Ålesund': { distance: '460 km', duration: '6 hours 30 mins' },
  'Molde': { distance: '440 km', duration: '6 hours' },
  'Kristiansund': { distance: '480 km', duration: '6 hours 30 mins' },
  'Florø': { distance: '520 km', duration: '7 hours 30 mins' },
  
  // Central Norway
  'Trondheim': { distance: '500 km', duration: '6 hours 30 mins' },
  'Steinkjer': { distance: '580 km', duration: '7 hours 30 mins' },
  'Røros': { distance: '390 km', duration: '5 hours' },
  'Oppdal': { distance: '380 km', duration: '4 hours 45 mins' },
  
  // Northern Norway
  'Bodø': { distance: '1270 km', duration: '15 hours' },
  'Tromsø': { distance: '1730 km', duration: '20 hours' },
  'Alta': { distance: '2130 km', duration: '25 hours' },
  'Hammerfest': { distance: '2230 km', duration: '26 hours' },
  
  // Nearby areas
  'Drammen': { distance: '45 km', duration: '45 mins' },
  'Fredrikstad': { distance: '90 km', duration: '1 hour 15 mins' },
  'Moss': { distance: '65 km', duration: '1 hour' },
  'Sarpsborg': { distance: '95 km', duration: '1 hour 20 mins' },
  'Tønsberg': { distance: '100 km', duration: '1 hour 30 mins' },
  'Sandefjord': { distance: '120 km', duration: '1 hour 45 mins' },
  'Larvik': { distance: '140 km', duration: '2 hours' },
  'Skien': { distance: '150 km', duration: '2 hours 15 mins' },
  'Porsgrunn': { distance: '145 km', duration: '2 hours' },
  'Notodden': { distance: '120 km', duration: '1 hour 45 mins' },
  'Kongsberg': { distance: '85 km', duration: '1 hour 15 mins' },
  'Hønefoss': { distance: '70 km', duration: '1 hour' },
  'Jessheim': { distance: '50 km', duration: '45 mins' },
  'Gardermoen': { distance: '50 km', duration: '45 mins' },
  
  // Mountain/ski areas
  'Geilo': { distance: '220 km', duration: '3 hours' },
  'Hemsedal': { distance: '200 km', duration: '2 hours 45 mins' },
  'Trysil': { distance: '240 km', duration: '3 hours 15 mins' },
  'Kvitfjell': { distance: '170 km', duration: '2 hours 15 mins' },
  'Beitostølen': { distance: '250 km', duration: '3 hours 30 mins' },
  'Gol': { distance: '200 km', duration: '2 hours 45 mins' },
  'Ål': { distance: '180 km', duration: '2 hours 30 mins' },
  'Nesbyen': { distance: '160 km', duration: '2 hours 15 mins' },
  'Fagernes': { distance: '180 km', duration: '2 hours 30 mins' },
  'Lom': { distance: '280 km', duration: '4 hours' },
  'Vågå': { distance: '260 km', duration: '3 hours 45 mins' },
  'Dombås': { distance: '300 km', duration: '4 hours 15 mins' },
  'Folldal': { distance: '320 km', duration: '4 hours 30 mins' },
  'Tynset': { distance: '280 km', duration: '4 hours' },
  'Rena': { distance: '200 km', duration: '2 hours 45 mins' },
  'Koppang': { distance: '220 km', duration: '3 hours' },
  'Alvdal': { distance: '240 km', duration: '3 hours 15 mins' },
  'Tolga': { distance: '260 km', duration: '3 hours 30 mins' },
  'Os': { distance: '280 km', duration: '4 hours' }
};

/**
 * Get fallback distance data for a Norwegian location
 * @param {string} location - Location name (e.g., "Bergen, Norway")
 * @returns {Object} Distance and duration data
 */
function getFallbackDistance(location) {
  if (!location) {
    return {
      distanceText: 'N/A',
      distanceValue: Infinity,
      durationText: 'N/A',
      durationValue: Infinity
    };
  }

  // Clean the location name
  const cleanLocation = location.replace(', Norway', '').trim();
  
  // Try exact match first
  if (norwegianDistances[cleanLocation]) {
    const data = norwegianDistances[cleanLocation];
    return {
      distanceText: data.distance,
      distanceValue: parseInt(data.distance) * 1000, // Convert to meters
      durationText: data.duration,
      durationValue: parseDurationToMinutes(data.duration)
    };
  }
  
  // Try partial matches for compound municipality names
  for (const [city, data] of Object.entries(norwegianDistances)) {
    if (cleanLocation.includes(city) || city.includes(cleanLocation)) {
      return {
        distanceText: data.distance,
        distanceValue: parseInt(data.distance) * 1000,
        durationText: data.duration,
        durationValue: parseDurationToMinutes(data.duration)
      };
    }
  }
  
  // Default for unknown locations (assume medium distance)
  return {
    distanceText: '~300 km',
    distanceValue: 300000,
    durationText: '~4 hours',
    durationValue: 240
  };
}

/**
 * Parse duration string to minutes
 * @param {string} duration - Duration like "2 hours 30 mins"
 * @returns {number} Total minutes
 */
function parseDurationToMinutes(duration) {
  let totalMinutes = 0;
  
  // Extract hours
  const hoursMatch = duration.match(/(\d+)\s*hours?/);
  if (hoursMatch) {
    totalMinutes += parseInt(hoursMatch[1]) * 60;
  }
  
  // Extract minutes
  const minutesMatch = duration.match(/(\d+)\s*mins?/);
  if (minutesMatch) {
    totalMinutes += parseInt(minutesMatch[1]);
  }
  
  return totalMinutes;
}

module.exports = {
  getFallbackDistance,
  norwegianDistances
};
