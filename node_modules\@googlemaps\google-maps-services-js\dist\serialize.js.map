{"version": 3, "file": "serialize.js", "sourceRoot": "", "sources": ["../src/serialize.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAIH,iCAAoC;AACpC,6DAA4D;AAC5D,gEAAuC;AAEvC,MAAM,EAAE,GAAG,sBAAW,CAAC,SAAS,CAAC;AAEjC,MAAM,SAAS,GAAG,GAAG,CAAC;AAEtB,SAAgB,cAAc,CAAC,CAAS;IACtC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9C,oBAAoB;IACtB,CAAC;SAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;QACpC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;SAAM,IAAI,UAAU,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;QAC/C,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,SAAS,EAAE,CAAC;IACxB,CAAC;IAED,OAAO,CAAC;SACL,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;IACtB,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAlBD,wCAkBC;AAED,SAAgB,+BAA+B,CAAC,CAAU;IACxD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QACrB,OAAO,MAAM,CAAC;IAChB,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AARD,0EAQC;AAED,SAAgB,cAAc,CAAC,CAAkB;IAC/C,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AARD,wCAQC;AAED,SAAgB,oBAAoB,CAAC,YAAmC;IACtE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;QACrC,OAAO,YAAY,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,OAAO,CACL,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC;YACtC,SAAS;YACT,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC,CACvC,CAAC;IACJ,CAAC;AACH,CAAC;AAVD,oDAUC;AAED,SAAgB,eAAe,CAAC,CAAS;IACvC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvC,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1C,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5B,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1C,CAAC;SAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,IAAI,UAAU,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;QAC/C,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC;IAC/C,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,SAAS,EAAE,CAAC;IACxB,CAAC;AACH,CAAC;AAdD,0CAcC;AAED,SAAgB,+BAA+B,CAAC,CAAoB;IAClE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,OAAO,CAAC,CAAC;IACX,CAAC;IAED,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC3D,MAAM,OAAO,GAAG,OAAO,IAAA,iBAAU,EAAC,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;IAE5D,IAAI,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAbD,0EAaC;AAKD,SAAgB,UAAU,CACxB,MAAwB,EACxB,OAAe,EACf,qBAA6B;IAC3B,WAAW,EAAE,WAAW;IACxB,oBAAoB,EAAE,SAAS;CAChC;IAED,8DAA8D;IAC9D,OAAO,CAAC,MAA2B,EAAE,EAAE;QACrC,wBAAwB;QACxB,MAAM,gBAAgB,qBAAQ,MAAM,CAAE,CAAC;QAEvC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,IAAI,GAAG,IAAI,gBAAgB,EAAE,CAAC;gBAC5B,gBAAgB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,IACE,WAAW,IAAI,gBAAgB;YAC/B,eAAe,IAAI,gBAAgB,EACnC,CAAC;YACD,gDAAgD;YAChD,OAAO,4BAA4B,CACjC,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,CACR,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC;AAjCD,gCAiCC;AAED,SAAgB,WAAW,CAAC,CAAwB;IAClD,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACtC,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AARD,kCAQC;AAED,SAAgB,4BAA4B,CAC1C,gBAA2C,EAC3C,kBAA0B,EAC1B,OAAe;IAEf,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC;IACrD,MAAM,YAAY,GAAG,gBAAgB,CAAC,aAAa,CAAC;IACpD,OAAO,gBAAgB,CAAC,SAAS,CAAC;IAClC,OAAO,gBAAgB,CAAC,aAAa,CAAC;IAEtC,MAAM,kBAAkB,GAAG,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;IACpE,MAAM,WAAW,GAAG,GAAG,OAAO,IAAI,kBAAkB,EAAE,CAAC;IACvD,MAAM,SAAS,GAAG,IAAA,+BAAe,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IAE7D,+BAA+B;IAC/B,OAAO,GAAG,kBAAkB,cAAc,SAAS,EAAE,CAAC;AACxD,CAAC;AAhBD,oEAgBC"}