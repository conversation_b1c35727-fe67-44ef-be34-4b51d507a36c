# 🏔️ Inatur Cabin Finder

A modern web application to search and sort cabin rentals from inatur.no by distance from Oslo, with Google Maps integration.

## ✨ Features

- **Smart Search**: Search cabins by date range and travel time preferences
- **Distance Sorting**: Automatically sorts results by driving distance from Oslo
- **Interactive Map**: View cabin locations on Google Maps with detailed info windows
- **Travel Time Filtering**: Filter cabins by minimum and maximum driving time
- **Responsive Design**: Works great on desktop and mobile devices
- **Caching**: Intelligent caching of Google Maps API calls to improve performance
- **Rate Limiting**: Built-in protection against API abuse
- **Error Handling**: Comprehensive error handling and user-friendly error pages

## 🚀 Quick Start

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager
- Google Maps API key (optional, but recommended for full functionality)

### Installation

1. **Clone or download the project**

   ```bash
   cd "Inatur sorting #1"
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables** (optional but recommended)

   ```bash
   # Copy the example environment file
   copy .env.example .env

   # Edit .env file and add your Google Maps API key
   # GOOGLE_API_KEY=your_actual_api_key_here
   ```

4. **Start the application**

   ```bash
   # For production
   npm start

   # For development (with auto-restart)
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Server Configuration
PORT=3000

# Google Maps API Configuration (get from Google Cloud Console)
GOOGLE_API_KEY=your_google_maps_api_key_here

# Application Configuration
DEFAULT_ORIGIN=Oslo, Norway
REQUEST_TIMEOUT=5000
CACHE_DURATION=300000
```

### Google Maps API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. **Enable billing** for your project (required for Google Maps APIs)
4. Enable the following APIs:
   - Maps JavaScript API
   - Geocoding API
   - Distance Matrix API
5. Create credentials (API Key)
6. **Configure API key restrictions** (optional but recommended):
   - Go to "Credentials" → Click your API key
   - Under "Application restrictions", select "HTTP referrers"
   - Add `http://localhost:3000/*` for local development
   - Under "API restrictions", select "Restrict key" and choose the 3 APIs above
7. Add the API key to your `.env` file

**Note**: The application will work without a Google Maps API key, but map functionality will be disabled.

### 🚨 Common Google Maps API Issues

**403 Forbidden Error**: This usually means:

1. **Billing not enabled** - Google Maps APIs require a billing account
2. **API not enabled** - Make sure all 3 APIs are enabled in your project
3. **Key restrictions** - Your API key might be restricted to certain domains
4. **Quota exceeded** - You've hit your daily/monthly usage limits

**Solutions**:

- Enable billing in Google Cloud Console
- Check that all required APIs are enabled
- Remove API key restrictions for testing
- Monitor your usage in the Google Cloud Console

## 📖 Usage

1. **Set Date Range**: Choose your desired check-in and check-out dates
2. **Set Travel Time** (optional): Filter by minimum and maximum driving time from Oslo
3. **Search**: Click "Search Cabins" to find available rentals
4. **Browse Results**: View cabins sorted by distance, with details and map locations
5. **Visit Listings**: Click on cabin titles to view full details on inatur.no

## 🛠️ API Endpoints

- `GET /` - Main search interface and results
- `GET /health` - Health check endpoint
- `GET /cache-stats` - Cache statistics (for debugging)

## 📁 Project Structure

```
├── server.js              # Main application file
├── package.json           # Dependencies and scripts
├── .env.example          # Environment variables template
├── README.md             # This file
├── config/
│   └── config.js         # Application configuration
├── services/
│   ├── googleMapsService.js  # Google Maps API integration
│   └── inaturService.js      # Inatur API integration
└── utils/
    └── helpers.js        # Utility functions
```

## 🔍 Troubleshooting

### Common Issues

1. **"Google Maps API key not configured"**

   - Add your API key to the `.env` file
   - Make sure the required APIs are enabled in Google Cloud Console

2. **"No cabins found"**

   - Try expanding your date range
   - Remove travel time restrictions
   - Check if the dates are in the future

3. **Slow loading**

   - The app fetches data from multiple APIs, which can take time
   - Results are cached to improve subsequent searches

4. **Rate limiting errors**
   - Wait a few minutes before making new requests
   - The app has built-in rate limiting for protection

### Performance Tips

- Use reasonable date ranges (avoid searching for entire months)
- The first search may be slower due to API calls, but subsequent searches use cached data
- Consider setting travel time limits to reduce the number of results

## 🚦 Development

### Available Scripts

```bash
npm start      # Start the production server
npm run dev    # Start development server with auto-restart
npm test       # Run tests (not implemented yet)
```

### Adding Features

The application is modular and easy to extend:

- Add new filters in the main route handler
- Extend the Google Maps service for additional location features
- Modify the HTML generation functions for UI changes
- Add new API endpoints as needed

## 📝 License

MIT License - feel free to use and modify as needed.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Happy cabin hunting! 🏕️**
