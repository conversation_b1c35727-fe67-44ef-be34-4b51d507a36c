/**
 * Configuration module for the Inatur Cabin Finder application
 */

require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost'
  },

  // Google Maps API configuration
  googleMaps: {
    apiKey: process.env.GOOGLE_API_KEY,
    timeout: parseInt(process.env.REQUEST_TIMEOUT) || 5000
  },

  // Application configuration
  app: {
    defaultOrigin: process.env.DEFAULT_ORIGIN || 'Oslo, Norway',
    cacheDuration: parseInt(process.env.CACHE_DURATION) || 300000, // 5 minutes
    defaultDaysAhead: 7,
    maxPages: 50 // Safety limit for API calls
  },

  // Rate limiting configuration
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  }
};

// Validate required configuration
if (!config.googleMaps.apiKey) {
  console.warn('WARNING: GOOGLE_API_KEY is not set. Google Maps functionality will not work.');
}

module.exports = config;
