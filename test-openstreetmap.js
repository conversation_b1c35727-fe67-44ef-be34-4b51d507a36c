/**
 * Test script for OpenStreetMap service
 */

const { openStreetMapService } = require('./services/googleMapsService');

async function testOpenStreetMap() {
  console.log('🧪 Testing OpenStreetMap service...');

  try {
    console.log('\n1️⃣ Testing geocoding...');
    const coordinates = await openStreetMapService.geocode('Bergen, Norway');
    console.log(`✅ Geocoding works! Bergen coordinates:`, coordinates);

    console.log('\n2️⃣ Testing distance calculation...');
    const distance = await openStreetMapService.getDistanceAndDuration('Oslo, Norway', 'Bergen, Norway');
    console.log(`✅ Distance calculation works! Oslo to Bergen:`, distance);

    console.log('\n🎉 OpenStreetMap service is working correctly!');

  } catch (error) {
    console.error('❌ Error testing OpenStreetMap service:', error.message);
  }
}

testOpenStreetMap();
