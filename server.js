/**
 * Inatur Cabin Finder - A web application to search and sort cabin rentals
 * from inatur.no by distance from Oslo with Google Maps integration
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const config = require('./config/config');
const googleMapsService = require('./services/googleMapsService');
const { openStreetMapService } = require('./services/googleMapsService');
const inaturService = require('./services/inaturService');
const { validateDate, validateNumber, safeGet } = require('./utils/helpers');

const app = express();

// Middleware setup
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

/**
 * Main route for searching and displaying cabin listings
 */
app.get('/', async (req, res) => {
  // If no query parameters, show the search form
  if (Object.keys(req.query).length === 0) {
    return res.send(generateSearchFormHtml());
  }

  // Otherwise, process the search
  try {
    // Validate and sanitize input parameters
    const today = new Date();
    const defaultEndDate = new Date();
    defaultEndDate.setDate(defaultEndDate.getDate() + config.app.defaultDaysAhead);

    const startDate = validateDate(req.query.startDate, today);
    const endDate = validateDate(req.query.endDate, defaultEndDate);
    const minTime = validateNumber(req.query.minTime, 0, 0, 24);
    const maxTime = validateNumber(req.query.maxTime, Infinity, 0, 24);
    const minPrice = validateNumber(req.query.minPrice, 0, 0, Infinity);
    const maxPrice = validateNumber(req.query.maxPrice, Infinity, 0, Infinity);
    const minRoadDistance = validateNumber(req.query.minRoadDistance, 0, 0, Infinity);
    const maxRoadDistance = validateNumber(req.query.maxRoadDistance, Infinity, 0, Infinity);

    console.log(`Processing request: ${startDate} to ${endDate}, travel time: ${minTime}-${maxTime} hours, price: ${minPrice}-${maxPrice === Infinity ? '∞' : maxPrice} NOK, road distance: ${minRoadDistance}-${maxRoadDistance === Infinity ? '∞' : maxRoadDistance} km`);

    // Fetch all listings from Inatur
    const listings = await inaturService.fetchAllListings(startDate, endDate);

    if (listings.length === 0) {
      return res.send(generateNoResultsHtml(startDate, endDate, minTime, maxTime, minPrice, maxPrice, minRoadDistance, maxRoadDistance));
    }

    // Enrich listings with distance and location data
    const enrichedListings = await enrichListingsWithLocationData(listings);

    // Sort by distance and filter by travel time
    const sortedListings = enrichedListings
      .sort((a, b) => safeGet(a, 'distanceData.distanceValue', Infinity) - safeGet(b, 'distanceData.distanceValue', Infinity));

    const filteredListings = sortedListings.filter(listing => {
      const durationHours = safeGet(listing, 'distanceData.durationValue', Infinity) / 60;
      const price = safeGet(listing, 'price', 0);
      const roadDistance = safeGet(listing, 'summerDistanceFromRoad', 0);

      const timeMatch = durationHours >= minTime && durationHours <= maxTime;
      const priceMatch = price >= minPrice && price <= maxPrice;
      const roadDistanceMatch = roadDistance >= minRoadDistance && roadDistance <= maxRoadDistance;

      return timeMatch && priceMatch && roadDistanceMatch;
    });

    // Generate and send HTML response
    const html = generateResultsHtml(filteredListings, startDate, endDate, minTime, maxTime, minPrice, maxPrice, minRoadDistance, maxRoadDistance);
    res.send(html);

  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).send(generateErrorHtml(error.message));
  }
});

/**
 * Enrich listings with distance and location data from Google Maps
 * @param {Array} listings - Array of cabin listings
 * @returns {Promise<Array>} Enriched listings with distance and location data
 */
async function enrichListingsWithLocationData(listings) {
  console.log(`Enriching ${listings.length} listings with location data...`);

  const enrichedListings = [];

  for (const [index, listing] of listings.entries()) {
    try {
      const cleanListing = inaturService.cleanListing(listing);
      const location = cleanListing.location;

      if (location) {
        // Get distance and duration data
        const distanceData = await googleMapsService.getDistanceAndDuration(
          config.app.defaultOrigin,
          location
        );

        // Get coordinates for map display
        const coordinates = await googleMapsService.getLatLng(location);

        cleanListing.distanceData = distanceData;
        cleanListing.latLng = coordinates;
      } else {
        // Fallback for listings without location
        cleanListing.distanceData = googleMapsService.getDefaultDistanceData();
        cleanListing.latLng = { lat: null, lng: null };
      }

      enrichedListings.push(cleanListing);

      // Progress logging
      if ((index + 1) % 10 === 0) {
        console.log(`Processed ${index + 1}/${listings.length} listings`);
      }

    } catch (error) {
      console.error(`Error enriching listing ${index}:`, error.message);
      // Add listing without enrichment
      enrichedListings.push(inaturService.cleanListing(listing));
    }
  }

  console.log(`Successfully enriched ${enrichedListings.length} listings`);
  return enrichedListings;
}

/**
 * Generate HTML for the initial search form
 * @returns {string} Complete HTML page with search form
 */
function generateSearchFormHtml() {
  const today = new Date().toISOString().split('T')[0];
  const defaultEndDate = new Date();
  defaultEndDate.setDate(defaultEndDate.getDate() + 7);
  const defaultEnd = defaultEndDate.toISOString().split('T')[0];

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏔️ Inatur Cabin Finder</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .feature {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .feature h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .feature p {
            margin: 0;
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .filters {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .filters h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 25px;
        }
        .filters form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            align-items: end;
        }
        .filters label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        .filters input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .filters input:focus {
            outline: none;
            border-color: #3498db;
        }
        .filters button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            grid-column: 1 / -1;
            justify-self: center;
            max-width: 300px;
        }
        .filters button:hover {
            background: #2980b9;
        }
        .filters button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .date-error {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }
        .input-error {
            border-color: #e74c3c !important;
            background-color: #fadbd8 !important;
        }
        .info-box {
            background: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .info-box h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .info-box ul {
            margin-bottom: 0;
        }
        .info-box li {
            margin-bottom: 5px;
        }

        /* Loading screen styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 6px solid #ecf0f1;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .loading-details {
            font-size: 14px;
            color: #7f8c8d;
            text-align: center;
            max-width: 400px;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            h1 {
                font-size: 2em;
            }
            .filters form {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script>
        // Date validation and form handling
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');
            const searchButton = document.getElementById('searchButton');

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            startDateInput.min = today;
            endDateInput.min = today;

            // Set maximum date to 1 year from now
            const maxDate = new Date();
            maxDate.setFullYear(maxDate.getFullYear() + 1);
            const maxDateStr = maxDate.toISOString().split('T')[0];
            startDateInput.max = maxDateStr;
            endDateInput.max = maxDateStr;

            // Date validation function
            function validateDates() {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                let isValid = true;
                let errorMessage = '';

                // Clear previous errors
                document.querySelectorAll('.date-error').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.input-error').forEach(el => el.classList.remove('input-error'));

                // Check if start date is in the past
                if (startDate < today) {
                    isValid = false;
                    errorMessage = 'Start date cannot be in the past';
                    startDateInput.classList.add('input-error');
                    showDateError(startDateInput, errorMessage);
                }

                // Check if end date is before start date
                if (endDate <= startDate) {
                    isValid = false;
                    errorMessage = 'End date must be after start date';
                    endDateInput.classList.add('input-error');
                    showDateError(endDateInput, errorMessage);
                }

                // Check minimum stay (at least 1 night)
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                if (diffDays < 1) {
                    isValid = false;
                    errorMessage = 'Minimum stay is 1 night';
                    endDateInput.classList.add('input-error');
                    showDateError(endDateInput, errorMessage);
                }

                // Check maximum stay (30 days)
                if (diffDays > 30) {
                    isValid = false;
                    errorMessage = 'Maximum stay is 30 days';
                    endDateInput.classList.add('input-error');
                    showDateError(endDateInput, errorMessage);
                }

                return isValid;
            }

            function showDateError(input, message) {
                let errorDiv = input.parentNode.querySelector('.date-error');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'date-error';
                    input.parentNode.appendChild(errorDiv);
                }
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }

            // Add event listeners for real-time validation
            startDateInput.addEventListener('change', validateDates);
            endDateInput.addEventListener('change', validateDates);

            // Form submission with loading screen
            form.addEventListener('submit', function(e) {
                if (!validateDates()) {
                    e.preventDefault();
                    return false;
                }

                // Show loading screen
                showLoadingScreen();

                // Allow form to submit normally
                return true;
            });

            function showLoadingScreen() {
                // Create loading overlay if it doesn't exist
                let loadingOverlay = document.getElementById('loadingOverlay');
                if (!loadingOverlay) {
                    loadingOverlay = document.createElement('div');
                    loadingOverlay.id = 'loadingOverlay';
                    loadingOverlay.className = 'loading-overlay';
                    loadingOverlay.innerHTML = \`
                        <div class="loading-spinner"></div>
                        <div class="loading-text">🔍 Searching for cabins...</div>
                        <div class="loading-details">
                            Fetching listings from inatur.no<br>
                            Calculating distances and travel times<br>
                            This may take 30-60 seconds
                        </div>
                    \`;
                    document.body.appendChild(loadingOverlay);
                }

                // Show the loading screen
                loadingOverlay.style.display = 'flex';

                // Disable the search button
                searchButton.disabled = true;
                searchButton.textContent = '⏳ Searching...';
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>🏔️ Inatur Cabin Finder</h1>
        <p class="subtitle">Find the perfect Norwegian cabin sorted by distance from Oslo</p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🗺️</div>
                <h3>Distance Sorting</h3>
                <p>Cabins sorted by driving distance from Oslo with real travel times</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💰</div>
                <h3>Price Filtering</h3>
                <p>Filter by price range to find cabins within your budget</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🛣️</div>
                <h3>Road Access</h3>
                <p>Filter by distance from road for easy or remote access</p>
            </div>
        </div>

        <div class="info-box">
            <h3>🎯 How it works:</h3>
            <ul>
                <li><strong>Smart Date Validation:</strong> Prevents past dates and ensures valid stays</li>
                <li><strong>Real Distance Calculation:</strong> Uses Google Maps for accurate driving distances</li>
                <li><strong>Multiple Filters:</strong> Combine travel time, price, and road access filters</li>
                <li><strong>Live Results:</strong> Fresh data from inatur.no with interactive maps</li>
            </ul>
        </div>

        <div class="filters">
            <h2>🔍 Search for Cabins</h2>
            <form method="GET" action="/">
                <div>
                    <label for="startDate">Check-in Date:</label>
                    <input type="date" id="startDate" name="startDate" value="${today}" required />
                </div>
                <div>
                    <label for="endDate">Check-out Date:</label>
                    <input type="date" id="endDate" name="endDate" value="${defaultEnd}" required />
                </div>
                <div>
                    <label for="minTime">Min Travel Time (hours):</label>
                    <input type="number" id="minTime" name="minTime" min="0" max="24" step="0.5" placeholder="0" />
                </div>
                <div>
                    <label for="maxTime">Max Travel Time (hours):</label>
                    <input type="number" id="maxTime" name="maxTime" min="0" max="24" step="0.5" placeholder="No limit" />
                </div>
                <div>
                    <label for="minPrice">Min Price (NOK):</label>
                    <input type="number" id="minPrice" name="minPrice" min="0" step="100" placeholder="0" />
                </div>
                <div>
                    <label for="maxPrice">Max Price (NOK):</label>
                    <input type="number" id="maxPrice" name="maxPrice" min="0" step="100" placeholder="No limit" />
                </div>
                <div>
                    <label for="minRoadDistance">Min Distance from Road (km):</label>
                    <input type="number" id="minRoadDistance" name="minRoadDistance" min="0" step="0.1" placeholder="0" />
                </div>
                <div>
                    <label for="maxRoadDistance">Max Distance from Road (km):</label>
                    <input type="number" id="maxRoadDistance" name="maxRoadDistance" min="0" step="0.1" placeholder="No limit" />
                </div>
                <button type="submit" id="searchButton">🔍 Search Cabins</button>
            </form>
        </div>
    </div>
</body>
</html>`;
}

/**
 * Generate HTML for successful results
 * @param {Array} listings - Filtered and sorted listings
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @param {number} minTime - Minimum travel time in hours
 * @param {number} maxTime - Maximum travel time in hours
 * @param {number} minPrice - Minimum price in NOK
 * @param {number} maxPrice - Maximum price in NOK
 * @param {number} minRoadDistance - Minimum distance from road in km
 * @param {number} maxRoadDistance - Maximum distance from road in km
 * @returns {string} Complete HTML page
 */
function generateResultsHtml(listings, startDate, endDate, minTime, maxTime, minPrice, maxPrice, minRoadDistance, maxRoadDistance) {
  const mapLocations = listings
    .filter(listing => listing.latLng && listing.latLng.lat && listing.latLng.lng)
    .map(listing => ({
      lat: listing.latLng.lat,
      lng: listing.latLng.lng,
      title: safeGet(listing, 'title', 'Unknown'),
      url: `https://www.inatur.no${safeGet(listing, 'url', '')}`,
      description: safeGet(listing, 'description', ''),
      price: safeGet(listing, 'price', 0),
      distance: safeGet(listing, 'distanceData.distanceText', 'N/A'),
      travelTime: safeGet(listing, 'distanceData.durationText', 'N/A')
    }));

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inatur Cabin Finder - Results</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .filters {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .filters form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        .filters label {
            font-weight: bold;
            color: #34495e;
        }
        .filters input, .filters button {
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }
        .filters button {
            background: #3498db;
            color: white;
            cursor: pointer;
            border: none;
            font-weight: bold;
        }
        .filters button:hover {
            background: #2980b9;
        }
        .stats {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            color: #7f8c8d;
        }
        #map {
            height: 500px;
            width: 100%;
            margin-bottom: 30px;
            border-radius: 5px;
            border: 1px solid #bdc3c7;
        }
        .listings {
            display: grid;
            gap: 20px;
        }
        .listing {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .listing:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .listing img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .listing-content h2 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .listing-content h2 a {
            text-decoration: none;
            color: inherit;
        }
        .listing-content h2 a:hover {
            color: #3498db;
        }
        .listing-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .detail-item {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #3498db;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
        }
        .error {
            color: #e74c3c;
            text-align: center;
            padding: 20px;
            background: #fadbd8;
            border-radius: 5px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .listing {
                grid-template-columns: 1fr;
                text-align: center;
            }
            .filters form {
                grid-template-columns: 1fr;
            }
        }

        /* Loading screen styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 6px solid #ecf0f1;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .loading-details {
            font-size: 14px;
            color: #7f8c8d;
            text-align: center;
            max-width: 400px;
            line-height: 1.4;
        }

        .date-error {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .input-error {
            border-color: #e74c3c !important;
            background-color: #fadbd8 !important;
        }
    </style>
    ${config.googleMaps.apiKey ? `<script src="https://maps.googleapis.com/maps/api/js?key=${config.googleMaps.apiKey}&callback=initMap" async defer></script>` : ''}
    <script>
        function initMap() {
            ${!config.googleMaps.apiKey ? 'console.warn("Google Maps API key not configured"); return;' : ''}

            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 6,
                center: { lat: 60.472, lng: 8.4689 },
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            const locations = ${JSON.stringify(mapLocations)};

            locations.forEach(location => {
                if (location.lat && location.lng) {
                    const marker = new google.maps.Marker({
                        position: { lat: location.lat, lng: location.lng },
                        map: map,
                        title: location.title,
                        animation: google.maps.Animation.DROP
                    });

                    const infoWindow = new google.maps.InfoWindow({
                        content: \`
                            <div style="max-width: 300px;">
                                <h3><a href="\${location.url}" target="_blank" style="color: #3498db; text-decoration: none;">\${location.title}</a></h3>
                                <p>\${location.description}</p>
                                <div style="display: grid; gap: 5px; margin-top: 10px;">
                                    <div><strong>Price:</strong> NOK \${location.price}</div>
                                    <div><strong>Distance:</strong> \${location.distance}</div>
                                    <div><strong>Travel time:</strong> \${location.travelTime}</div>
                                </div>
                            </div>
                        \`
                    });

                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });
                }
            });
        }
    </script>

    <script>
        // Date validation and form handling
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');
            const searchButton = document.getElementById('searchButton');

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            startDateInput.min = today;
            endDateInput.min = today;

            // Set maximum date to 1 year from now
            const maxDate = new Date();
            maxDate.setFullYear(maxDate.getFullYear() + 1);
            const maxDateStr = maxDate.toISOString().split('T')[0];
            startDateInput.max = maxDateStr;
            endDateInput.max = maxDateStr;

            // Date validation function
            function validateDates() {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                let isValid = true;
                let errorMessage = '';

                // Clear previous errors
                document.querySelectorAll('.date-error').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.input-error').forEach(el => el.classList.remove('input-error'));

                // Check if start date is in the past
                if (startDate < today) {
                    isValid = false;
                    errorMessage = 'Start date cannot be in the past';
                    startDateInput.classList.add('input-error');
                    showDateError(startDateInput, errorMessage);
                }

                // Check if end date is before start date
                if (endDate <= startDate) {
                    isValid = false;
                    errorMessage = 'End date must be after start date';
                    endDateInput.classList.add('input-error');
                    showDateError(endDateInput, errorMessage);
                }

                // Check minimum stay (at least 1 night)
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                if (diffDays < 1) {
                    isValid = false;
                    errorMessage = 'Minimum stay is 1 night';
                    endDateInput.classList.add('input-error');
                    showDateError(endDateInput, errorMessage);
                }

                // Check maximum stay (30 days)
                if (diffDays > 30) {
                    isValid = false;
                    errorMessage = 'Maximum stay is 30 days';
                    endDateInput.classList.add('input-error');
                    showDateError(endDateInput, errorMessage);
                }

                return isValid;
            }

            function showDateError(input, message) {
                let errorDiv = input.parentNode.querySelector('.date-error');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'date-error';
                    input.parentNode.appendChild(errorDiv);
                }
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }

            // Add event listeners for real-time validation
            startDateInput.addEventListener('change', validateDates);
            endDateInput.addEventListener('change', validateDates);

            // Form submission with loading screen
            form.addEventListener('submit', function(e) {
                if (!validateDates()) {
                    e.preventDefault();
                    return false;
                }

                // Show loading screen
                showLoadingScreen();

                // Allow form to submit normally
                return true;
            });

            function showLoadingScreen() {
                // Create loading overlay if it doesn't exist
                let loadingOverlay = document.getElementById('loadingOverlay');
                if (!loadingOverlay) {
                    loadingOverlay = document.createElement('div');
                    loadingOverlay.id = 'loadingOverlay';
                    loadingOverlay.className = 'loading-overlay';
                    loadingOverlay.innerHTML = \`
                        <div class="loading-spinner"></div>
                        <div class="loading-text">🔍 Searching for cabins...</div>
                        <div class="loading-details">
                            Fetching listings from inatur.no<br>
                            Calculating distances and travel times<br>
                            This may take 30-60 seconds
                        </div>
                    \`;
                    document.body.appendChild(loadingOverlay);
                }

                // Show the loading screen
                loadingOverlay.style.display = 'flex';

                // Disable the search button
                searchButton.disabled = true;
                searchButton.textContent = '⏳ Searching...';
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>🏔️ Inatur Cabin Finder</h1>

        <div class="filters">
            <form method="GET" action="/">
                <div>
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" value="${startDate}" required />
                </div>
                <div>
                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" name="endDate" value="${endDate}" required />
                </div>
                <div>
                    <label for="minTime">Min Travel Time (hours):</label>
                    <input type="number" id="minTime" name="minTime" min="0" max="24" step="0.5" value="${minTime || ''}" placeholder="0" />
                </div>
                <div>
                    <label for="maxTime">Max Travel Time (hours):</label>
                    <input type="number" id="maxTime" name="maxTime" min="0" max="24" step="0.5" value="${maxTime === Infinity ? '' : maxTime}" placeholder="No limit" />
                </div>
                <div>
                    <label for="minPrice">Min Price (NOK):</label>
                    <input type="number" id="minPrice" name="minPrice" min="0" step="100" value="${minPrice || ''}" placeholder="0" />
                </div>
                <div>
                    <label for="maxPrice">Max Price (NOK):</label>
                    <input type="number" id="maxPrice" name="maxPrice" min="0" step="100" value="${maxPrice === Infinity ? '' : maxPrice}" placeholder="No limit" />
                </div>
                <div>
                    <label for="minRoadDistance">Min Distance from Road (km):</label>
                    <input type="number" id="minRoadDistance" name="minRoadDistance" min="0" step="0.1" value="${minRoadDistance || ''}" placeholder="0" />
                </div>
                <div>
                    <label for="maxRoadDistance">Max Distance from Road (km):</label>
                    <input type="number" id="maxRoadDistance" name="maxRoadDistance" min="0" step="0.1" value="${maxRoadDistance === Infinity ? '' : maxRoadDistance}" placeholder="No limit" />
                </div>
                <button type="submit" id="searchButton">🔍 Search Cabins</button>
            </form>
        </div>

        <div class="stats">
            Found <strong>${listings.length}</strong> cabin${listings.length !== 1 ? 's' : ''} matching your criteria
        </div>

        ${config.googleMaps.apiKey ? '<div id="map"></div>' : '<div class="error">Google Maps API key not configured. Map functionality disabled.</div>'}

        <div class="listings">
            ${listings.map(listing => `
                <div class="listing">
                    <img src="${safeGet(listing, 'thumbnail', '')}" alt="${safeGet(listing, 'title', 'Cabin')}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OSI+Tm8gSW1hZ2U8L3RleHQ+PC9zdmc+'" />
                    <div class="listing-content">
                        <h2><a href="https://www.inatur.no${safeGet(listing, 'url', '')}" target="_blank">${safeGet(listing, 'title', 'Unknown Cabin')}</a></h2>
                        <p>${safeGet(listing, 'description', 'No description available')}</p>
                        <div class="listing-details">
                            <div class="detail-item">
                                <span class="detail-label">Price:</span> NOK ${safeGet(listing, 'price', 0)}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Beds:</span> ${safeGet(listing, 'beds', 0)}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Distance:</span> ${safeGet(listing, 'distanceData.distanceText', 'N/A')}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Travel Time:</span> ${safeGet(listing, 'distanceData.durationText', 'N/A')}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Distance from Road:</span> ${safeGet(listing, 'summerDistanceFromRoad', 0)} km
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
}

/**
 * Generate HTML for no results found
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @param {number} minTime - Minimum travel time in hours
 * @param {number} maxTime - Maximum travel time in hours
 * @param {number} minPrice - Minimum price in NOK
 * @param {number} maxPrice - Maximum price in NOK
 * @param {number} minRoadDistance - Minimum distance from road in km
 * @param {number} maxRoadDistance - Maximum distance from road in km
 * @returns {string} HTML page for no results
 */
function generateNoResultsHtml(startDate, endDate, minTime, maxTime, minPrice, maxPrice, minRoadDistance, maxRoadDistance) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inatur Cabin Finder - No Results</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        .suggestions {
            text-align: left;
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .suggestions h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .suggestions ul {
            color: #34495e;
        }
        .back-button {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .back-button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 No Cabins Found</h1>
        <div class="message">
            No cabins were found for your search criteria:<br>
            <strong>Dates:</strong> ${startDate} to ${endDate}<br>
            <strong>Travel Time:</strong> ${minTime === 0 ? 'No minimum' : minTime + ' hours'} - ${maxTime === Infinity ? 'No maximum' : maxTime + ' hours'}<br>
            <strong>Price Range:</strong> ${minPrice === 0 ? 'No minimum' : 'NOK ' + minPrice} - ${maxPrice === Infinity ? 'No maximum' : 'NOK ' + maxPrice}<br>
            <strong>Distance from Road:</strong> ${minRoadDistance === 0 ? 'No minimum' : minRoadDistance + ' km'} - ${maxRoadDistance === Infinity ? 'No maximum' : maxRoadDistance + ' km'}
        </div>

        <div class="suggestions">
            <h3>Try adjusting your search:</h3>
            <ul>
                <li>Expand your date range</li>
                <li>Increase the maximum travel time</li>
                <li>Remove the minimum travel time restriction</li>
                <li>Increase the maximum price limit</li>
                <li>Remove the minimum price restriction</li>
                <li>Increase the maximum distance from road</li>
                <li>Remove the minimum distance from road restriction</li>
                <li>Try different dates when more cabins might be available</li>
            </ul>
        </div>

        <a href="/" class="back-button">← Back to Search</a>
    </div>
</body>
</html>`;
}

/**
 * Generate HTML for error pages
 * @param {string} errorMessage - Error message to display
 * @returns {string} HTML error page
 */
function generateErrorHtml(errorMessage) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inatur Cabin Finder - Error</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .error-message {
            background: #fadbd8;
            color: #e74c3c;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        .back-button {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .back-button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚠️ Error</h1>
        <div class="error-message">
            ${errorMessage || 'An unexpected error occurred while processing your request.'}
        </div>
        <p>Please try again later or contact support if the problem persists.</p>
        <a href="/" class="back-button">← Back to Search</a>
    </div>
</body>
</html>`;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    googleMapsConfigured: !!config.googleMaps.apiKey
  });
});

// Cache status endpoint (for debugging)
app.get('/cache-stats', (req, res) => {
  res.json({
    openStreetMap: openStreetMapService.getCacheStats(),
    googleMaps: googleMapsService.getCacheStats()
  });
});

// Start the server
const server = app.listen(config.server.port, () => {
  console.log(`🚀 Inatur Cabin Finder server is running!`);
  console.log(`📍 URL: http://localhost:${config.server.port}`);
  console.log(`🗺️  Google Maps: ${config.googleMaps.apiKey ? 'Configured' : 'Not configured (map functionality disabled)'}`);
  console.log(`⏰ Cache duration: ${config.app.cacheDuration / 1000} seconds`);
  console.log(`🛡️  Rate limit: ${config.rateLimit.max} requests per ${config.rateLimit.windowMs / 60000} minutes`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});