/**
 * Google Maps API service for geocoding and distance calculations
 */

const { Client } = require('@googlemaps/google-maps-services-js');
const config = require('../config/config');
const { durationToMinutes, delay, safeGet } = require('../utils/helpers');
const { getFallbackDistance } = require('../utils/fallbackDistances');

class GoogleMapsService {
  constructor() {
    this.client = new Client({});
    this.cache = new Map();
    this.lastRequestTime = 0;
    this.minRequestInterval = 100; // Minimum 100ms between requests
  }

  /**
   * Rate limit API requests to avoid hitting quotas
   */
  async rateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      await delay(this.minRequestInterval - timeSinceLastRequest);
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Get latitude and longitude coordinates for an address
   * @param {string} address - The address to geocode
   * @returns {Promise<{lat: number|null, lng: number|null}>} Coordinates or null values
   */
  async getLatLng(address) {
    if (!address || !config.googleMaps.apiKey) {
      return { lat: null, lng: null };
    }

    // Check cache first
    const cacheKey = `geocode_${address}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await this.client.geocode({
        params: {
          address: address,
          key: config.googleMaps.apiKey,
        },
        timeout: config.googleMaps.timeout,
      });

      const result = safeGet(response, 'data.results.0.geometry.location', null);

      const coordinates = result ? {
        lat: result.lat,
        lng: result.lng
      } : { lat: null, lng: null };

      // Cache the result
      this.cache.set(cacheKey, {
        data: coordinates,
        timestamp: Date.now()
      });

      return coordinates;

    } catch (error) {
      console.error(`Error geocoding address "${address}":`, error.message);

      // For 403 errors, provide approximate coordinates for Norwegian locations
      if (error.message.includes('403') && address.includes('Norway')) {
        return this.getFallbackCoordinates(address);
      }

      return { lat: null, lng: null };
    }
  }

  /**
   * Get distance and duration between origin and destination
   * @param {string} origin - Starting location
   * @param {string} destination - Destination location
   * @returns {Promise<Object>} Distance and duration data
   */
  async getDistanceAndDuration(origin, destination) {
    if (!origin || !destination || !config.googleMaps.apiKey) {
      return this.getDefaultDistanceData();
    }

    // Check cache first
    const cacheKey = `distance_${origin}_${destination}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await this.client.distancematrix({
        params: {
          origins: [origin],
          destinations: [destination],
          key: config.googleMaps.apiKey,
          mode: 'driving',
        },
        timeout: config.googleMaps.timeout,
      });

      const element = safeGet(response, 'data.rows.0.elements.0', null);

      if (element && element.status === 'OK') {
        const durationText = safeGet(element, 'duration.text', 'N/A');
        const distanceData = {
          distanceText: safeGet(element, 'distance.text', 'N/A'),
          distanceValue: safeGet(element, 'distance.value', Infinity),
          durationText: durationText,
          durationValue: durationToMinutes(durationText),
        };

        // Cache the result
        this.cache.set(cacheKey, {
          data: distanceData,
          timestamp: Date.now()
        });

        return distanceData;
      } else {
        return this.getDefaultDistanceData();
      }

    } catch (error) {
      console.error(`Error calculating distance from "${origin}" to "${destination}":`, error.message);

      // Use fallback distance calculation for Norwegian locations
      if (error.message.includes('403') && destination.includes('Norway')) {
        console.log(`Using fallback distance for ${destination}`);
        return getFallbackDistance(destination);
      }

      return this.getDefaultDistanceData();
    }
  }

  /**
   * Get fallback coordinates for Norwegian locations
   * @param {string} address - Address to get coordinates for
   * @returns {Object} Approximate coordinates
   */
  getFallbackCoordinates(address) {
    // Approximate coordinates for major Norwegian regions
    const norwegianCoordinates = {
      'Oslo': { lat: 59.9139, lng: 10.7522 },
      'Bergen': { lat: 60.3913, lng: 5.3221 },
      'Trondheim': { lat: 63.4305, lng: 10.3951 },
      'Stavanger': { lat: 58.9700, lng: 5.7331 },
      'Kristiansand': { lat: 58.1599, lng: 7.9956 },
      'Tromsø': { lat: 69.6492, lng: 18.9553 },
      'Drammen': { lat: 59.7439, lng: 10.2045 },
      'Fredrikstad': { lat: 59.2181, lng: 10.9298 },
      'Lillehammer': { lat: 61.1153, lng: 10.4662 },
      'Bodø': { lat: 67.2804, lng: 14.4049 },
      'Ålesund': { lat: 62.4722, lng: 6.1495 },
      'Molde': { lat: 62.7378, lng: 7.1618 },
      'Haugesund': { lat: 59.4138, lng: 5.2681 }
    };

    const cleanLocation = address.replace(', Norway', '').trim();

    // Try exact match
    if (norwegianCoordinates[cleanLocation]) {
      return norwegianCoordinates[cleanLocation];
    }

    // Try partial match
    for (const [city, coords] of Object.entries(norwegianCoordinates)) {
      if (cleanLocation.includes(city) || city.includes(cleanLocation)) {
        return coords;
      }
    }

    // Default to central Norway coordinates
    return { lat: 61.0, lng: 9.0 };
  }

  /**
   * Get default distance data for failed requests
   * @returns {Object} Default distance data
   */
  getDefaultDistanceData() {
    return {
      distanceText: 'N/A',
      distanceValue: Infinity,
      durationText: 'N/A',
      durationValue: Infinity
    };
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = new GoogleMapsService();
