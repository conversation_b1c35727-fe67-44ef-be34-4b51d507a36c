/**
 * Google Maps API service for geocoding and distance calculations
 */

const { Client } = require('@googlemaps/google-maps-services-js');
const config = require('../config/config');
const { durationToMinutes, delay, safeGet } = require('../utils/helpers');

class GoogleMapsService {
  constructor() {
    this.client = new Client({});
    this.cache = new Map();
    this.lastRequestTime = 0;
    this.minRequestInterval = 100; // Minimum 100ms between requests
  }

  /**
   * Rate limit API requests to avoid hitting quotas
   */
  async rateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      await delay(this.minRequestInterval - timeSinceLastRequest);
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Get latitude and longitude coordinates for an address
   * @param {string} address - The address to geocode
   * @returns {Promise<{lat: number|null, lng: number|null}>} Coordinates or null values
   */
  async getLatLng(address) {
    if (!address || !config.googleMaps.apiKey) {
      return { lat: null, lng: null };
    }

    // Check cache first
    const cacheKey = `geocode_${address}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await this.client.geocode({
        params: {
          address: address,
          key: config.googleMaps.apiKey,
        },
        timeout: config.googleMaps.timeout,
      });

      const result = safeGet(response, 'data.results.0.geometry.location', null);

      const coordinates = result ? {
        lat: result.lat,
        lng: result.lng
      } : { lat: null, lng: null };

      // Cache the result
      this.cache.set(cacheKey, {
        data: coordinates,
        timestamp: Date.now()
      });

      return coordinates;

    } catch (error) {
      console.error(`Error geocoding address "${address}":`, error.message);
      return { lat: null, lng: null };
    }
  }

  /**
   * Get distance and duration between origin and destination
   * @param {string} origin - Starting location
   * @param {string} destination - Destination location
   * @returns {Promise<Object>} Distance and duration data
   */
  async getDistanceAndDuration(origin, destination) {
    if (!origin || !destination || !config.googleMaps.apiKey) {
      return this.getDefaultDistanceData();
    }

    // Check cache first
    const cacheKey = `distance_${origin}_${destination}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await this.client.distancematrix({
        params: {
          origins: [origin],
          destinations: [destination],
          key: config.googleMaps.apiKey,
          mode: 'driving',
        },
        timeout: config.googleMaps.timeout,
      });

      const element = safeGet(response, 'data.rows.0.elements.0', null);

      if (element && element.status === 'OK') {
        const durationText = safeGet(element, 'duration.text', 'N/A');
        const distanceData = {
          distanceText: safeGet(element, 'distance.text', 'N/A'),
          distanceValue: safeGet(element, 'distance.value', Infinity),
          durationText: durationText,
          durationValue: durationToMinutes(durationText),
        };

        // Cache the result
        this.cache.set(cacheKey, {
          data: distanceData,
          timestamp: Date.now()
        });

        return distanceData;
      } else {
        return this.getDefaultDistanceData();
      }

    } catch (error) {
      console.error(`Error calculating distance from "${origin}" to "${destination}":`, error.message);
      return this.getDefaultDistanceData();
    }
  }

  /**
   * Get default distance data for failed requests
   * @returns {Object} Default distance data
   */
  getDefaultDistanceData() {
    return {
      distanceText: 'N/A',
      distanceValue: Infinity,
      durationText: 'N/A',
      durationValue: Infinity
    };
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = new GoogleMapsService();

/**
 * OpenStreetMap-based routing service using OpenRouteService API
 * Free alternative to Google Maps for distance and routing calculations
 */

const axios = require('axios');

class OpenStreetMapService {
  constructor() {
    // Using free OpenStreetMap services - no API key required!
    this.nominatimUrl = 'https://nominatim.openstreetmap.org'; // Free geocoding
    this.osrmUrl = 'https://router.project-osrm.org'; // Free routing
    this.cache = new Map();
    this.lastRequestTime = 0;
    this.minRequestInterval = 1000; // 1 second between requests (be respectful to free services)
  }

  /**
   * Rate limit API requests
   */
  async rateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      await delay(this.minRequestInterval - timeSinceLastRequest);
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Geocode an address to get coordinates
   * @param {string} address - Address to geocode
   * @returns {Promise<{lat: number|null, lng: number|null}>} Coordinates
   */
  async geocode(address) {
    if (!address) {
      return { lat: null, lng: null };
    }

    // Check cache first
    const cacheKey = `geocode_${address}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await axios.get(`${this.nominatimUrl}/search`, {
        params: {
          q: address,
          format: 'json',
          countrycodes: 'no', // Limit to Norway
          limit: 1,
          addressdetails: 1
        },
        timeout: config.googleMaps.timeout,
        headers: {
          'User-Agent': 'Inatur-Cabin-Finder/1.0 (https://github.com/user/inatur-cabin-finder)'
        }
      });

      const results = response.data;

      if (results && results.length > 0) {
        const result = {
          lat: parseFloat(results[0].lat),
          lng: parseFloat(results[0].lon)
        };

        // Cache the result
        this.cache.set(cacheKey, {
          data: result,
          timestamp: Date.now()
        });

        return result;
      }

      return { lat: null, lng: null };

    } catch (error) {
      console.error(`Error geocoding "${address}" with Nominatim:`, error.message);
      return { lat: null, lng: null };
    }
  }

  /**
   * Calculate driving distance and duration between two points
   * @param {string} origin - Starting location
   * @param {string} destination - Destination location
   * @returns {Promise<Object>} Distance and duration data
   */
  async getDistanceAndDuration(origin, destination) {
    if (!origin || !destination) {
      return this.getDefaultDistanceData();
    }

    // Check cache first
    const cacheKey = `route_${origin}_${destination}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      // First, geocode both locations
      const [originCoords, destCoords] = await Promise.all([
        this.geocode(origin),
        this.geocode(destination)
      ]);

      if (!originCoords.lat || !destCoords.lat) {
        console.log(`Could not geocode locations: ${origin} -> ${destination}`);
        return this.getDefaultDistanceData();
      }

      await this.rateLimit();

      // Get driving directions using OSRM
      const response = await axios.get(`${this.osrmUrl}/route/v1/driving/${originCoords.lng},${originCoords.lat};${destCoords.lng},${destCoords.lat}`, {
        params: {
          overview: 'false',
          alternatives: 'false',
          steps: 'false'
        },
        timeout: config.googleMaps.timeout,
        headers: {
          'User-Agent': 'Inatur-Cabin-Finder/1.0 (https://github.com/user/inatur-cabin-finder)'
        }
      });

      const routes = safeGet(response.data, 'routes', []);

      if (routes.length > 0) {
        const route = routes[0];

        const distanceMeters = safeGet(route, 'distance', 0);
        const durationSeconds = safeGet(route, 'duration', 0);

        const distanceKm = Math.round(distanceMeters / 1000);
        const durationMinutes = Math.round(durationSeconds / 60);

        const distanceData = {
          distanceText: `${distanceKm} km`,
          distanceValue: distanceMeters,
          durationText: this.formatDuration(durationMinutes),
          durationValue: durationMinutes,
        };

        // Cache the result
        this.cache.set(cacheKey, {
          data: distanceData,
          timestamp: Date.now()
        });

        return distanceData;
      }

      return this.getDefaultDistanceData();

    } catch (error) {
      console.error(`Error calculating route from "${origin}" to "${destination}":`, error.message);
      return this.getDefaultDistanceData();
    }
  }

  /**
   * Format duration in minutes to human-readable string
   * @param {number} minutes - Duration in minutes
   * @returns {string} Formatted duration
   */
  formatDuration(minutes) {
    if (minutes < 60) {
      return `${minutes} mins`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    }

    return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} mins`;
  }

  /**
   * Get default distance data for failed requests
   * @returns {Object} Default distance data
   */
  getDefaultDistanceData() {
    return {
      distanceText: 'N/A',
      distanceValue: Infinity,
      durationText: 'N/A',
      durationValue: Infinity
    };
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

// Export both services
module.exports.openStreetMapService = new OpenStreetMapService();
