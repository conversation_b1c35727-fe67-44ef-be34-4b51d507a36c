{"version": 3, "file": "client.test.js", "sourceRoot": "", "sources": ["../src/client.test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;AAEH,qCAsBkB;AAElB,kDAA0B;AAE1B,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE;IACtC,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAC;IAC9B,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AAChD,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+CAA+C,EAAE,GAAG,EAAE;IACzD,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,aAAa,EAAE,eAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/D,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AAChD,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,oEAAoE,EAAE,GAAG,EAAE;IAC9E,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,aAAa,EAAE,eAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/D,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACpE,kBAAS,CACV,CAAC;IACF,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CACtE,uBAAc,CACf,CAAC;IACF,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CACtD,eAAK,CAAC,QAAQ,CAAC,OAAO,CACvB,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kEAAkE,EAAE,GAAG,EAAE;IAC5E,MAAM,CAAC,GAAG,EAAE;QACV,IAAI,eAAM,CAAC;YACT,aAAa,EAAE,6BAAoB;YACnC,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;SAC3B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;AACpB,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,gDAAgD,EAAE,GAAG,EAAE;IAC1D,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;IACvE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACzE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACpE,kBAAS,CACV,CAAC;IACF,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CACtE,uBAAc,CACf,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,4DAA4D,EAAE,GAAG,EAAE;IACtE,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC;QACxB,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,UAAU,EAAE,EAAE;KACvE,CAAC,CAAC;IACH,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACzE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACpE,kBAAS,CACV,CAAC;IACF,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CACtE,UAAU,CACX,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mDAAmD,EAAE,GAAG,EAAE;IAC7D,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/D,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CACpE,kBAAS,CACV,CAAC;IACF,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CACtE,uBAAc,CACf,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,8CAA8C,EAAE,GAAG,EAAE;IACxD,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,YAAY,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5D,MAAM,CACJ,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,kCAAyB,CAAC,CACpE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACvB,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE;IACjD,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3B,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;IACjD,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,gEAAgE,EAAE,GAAG,EAAE;IAC1E,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAC;IAC9B,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,kCAAyB,CAAC,GAAG,KAAK,CAAC;IAC5E,MAAM,CAAC,iBAAiB,EAAE,CAAC;IAC3B,MAAM,CACJ,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,kCAAyB,CAAC,CACpE,CAAC,aAAa,EAAE,CAAC;IAClB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC5C,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE;IACjD,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAC;IAC9B,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3B,MAAM,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/B,MAAM,CACJ,MAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,kCAAyB,CAAC,CACpE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACrB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,sCAAsC,EAAE,GAAG,EAAE;IACpD,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAC;IAE9B,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC7C,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;QAC3C,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,CAAC,UAAU,CAAC,EAAuB,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE;QACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,MAAM,CAAC,cAAc,CAAC,EAA2B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QACzC,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,CAAC,SAAS,CAAC,EAAsB,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,EAAqB,CAAC,CAAC;QACvC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QACzC,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,CAAC,SAAS,CAAC,EAAsB,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC1C,MAAM,OAAO,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC7C,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3C,MAAM,CAAC,OAAO,CAAC,EAAoB,CAAC,CAAC;QACrC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE;QACjD,MAAM,cAAc,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,CAAC,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACzD,MAAM,CAAC,cAAc,CAAC,EAA2B,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACpD,MAAM,iBAAiB,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,MAAM,CAAC,iBAAiB,CAAC,EAA8B,CAAC,CAAC;QACzD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC/C,MAAM,YAAY,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACjD,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,MAAM,CAAC,YAAY,CAAC,EAAyB,CAAC,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACpD,MAAM,iBAAiB,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAChE,MAAM,IAAI,GAAG,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/D,MAAM,CAAC,iBAAiB,CAAC,EAA8B,CAAC,CAAC;QACzD,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC7C,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC7C,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,CAAC,UAAU,CAAC,EAAuB,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC/C,MAAM,YAAY,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACtD,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,MAAM,CAAC,YAAY,CAAC,EAAyB,CAAC,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+CAA+C,EAAE,GAAG,EAAE;QACzD,MAAM,sBAAsB,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACrE,MAAM,IAAI,GAAG,CAAC,sBAAsB,CAAC,sBAAsB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACzE,MAAM,CAAC,sBAAsB,CAAC,EAAmC,CAAC,CAAC;QACnE,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC7C,MAAM,UAAU,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAClD,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,CAAC,UAAU,CAAC,EAAuB,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC/C,MAAM,YAAY,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACrD,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,MAAM,CAAC,YAAY,CAAC,EAAyB,CAAC,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC9C,MAAM,WAAW,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,MAAM,CAAC,WAAW,CAAC,EAAwB,CAAC,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}