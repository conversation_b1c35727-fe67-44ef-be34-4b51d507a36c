var urlSignature=function(e,t,r){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=n(t),u=n(r);function c(e,t){var r=function(e){var t=e.replace(/-/g,"+").replace(/_/g,"/");return a.default.parse(t)}(t);return u.default(e,r).toString(a.default).replace(/\+/g,"-").replace(/\//g,"_")}function i(e,t){return"string"==typeof e&&(e=new URL(e)),c("".concat(e.pathname).concat(e.search),t)}return e.createSignature=i,e.createSignatureForPathAndQuery=c,e.signUrl=function(e,t){return"string"==typeof e&&(e=new URL(e)),new URL(e.toString()+"&signature="+i(e,t))},Object.defineProperty(e,"__esModule",{value:!0}),e}({},Base64,HmacSHA1);
