/**
 * Utility functions for the Inatur Cabin Finder application
 */

/**
 * Converts duration text (like "2 hours 30 mins") to total minutes
 * @param {string} durationText - The duration text to convert
 * @returns {number} Total duration in minutes
 */
const durationToMinutes = (durationText) => {
  if (!durationText || typeof durationText !== 'string') {
    return 0;
  }

  const durationArray = durationText.toLowerCase().split(' ');
  let minutes = 0;

  for (let i = 0; i < durationArray.length; i += 2) {
    const value = parseInt(durationArray[i], 10);
    const unit = durationArray[i + 1];

    if (isNaN(value)) continue;

    if (unit && unit.startsWith('hour')) {
      minutes += value * 60;
    } else if (unit && unit.startsWith('min')) {
      minutes += value;
    }
  }

  return minutes;
};

/**
 * Validates and sanitizes date input
 * @param {string} dateString - Date string to validate
 * @param {Date} defaultDate - Default date to use if invalid
 * @returns {string} Valid date string in YYYY-MM-DD format
 */
const validateDate = (dateString, defaultDate = new Date()) => {
  if (!dateString) {
    return defaultDate.toISOString().split('T')[0];
  }

  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return defaultDate.toISOString().split('T')[0];
  }

  return date.toISOString().split('T')[0];
};

/**
 * Validates and sanitizes numeric input
 * @param {string|number} value - Value to validate
 * @param {number} defaultValue - Default value if invalid
 * @param {number} min - Minimum allowed value
 * @param {number} max - Maximum allowed value
 * @returns {number} Valid numeric value
 */
const validateNumber = (value, defaultValue = 0, min = 0, max = Infinity) => {
  const num = parseFloat(value);
  if (isNaN(num) || num < min || num > max) {
    return defaultValue;
  }
  return num;
};

/**
 * Creates a delay for rate limiting API calls
 * @param {number} ms - Milliseconds to delay
 * @returns {Promise} Promise that resolves after the delay
 */
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Safely extracts nested object properties
 * @param {Object} obj - Object to extract from
 * @param {string} path - Dot-separated path to the property
 * @param {*} defaultValue - Default value if property doesn't exist
 * @returns {*} The property value or default value
 */
const safeGet = (obj, path, defaultValue = null) => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : defaultValue;
  }, obj);
};

module.exports = {
  durationToMinutes,
  validateDate,
  validateNumber,
  delay,
  safeGet
};
