{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iDAAmC;AAEnC,6CAIsB;AACtB,yCAIoB;AACpB,2CAA6E;AAC7E,kEAIoC;AACpC,+CAA6E;AAC7E,2CAA6E;AAC7E,uDAI8B;AAC9B,wDAI+B;AAC/B,8CAI0B;AAC1B,0CAIwB;AACxB,kEAIoC;AACpC,wDAI+B;AAC/B,6DAIkC;AAClC,qDAI6B;AAC7B,oDAI6B;AAC7B,yCAAyE;AACzE,kDAAiE;AAEjE,mDAA4C;AAC5C,uCAA0C;AAE1C,mDAAmD;AACtC,QAAA,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;AAC7C,QAAA,iBAAiB,GAAG,IAAI,2BAAU,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AACxD,QAAA,cAAc,GAAG,KAAK,CAAC;AACvB,QAAA,SAAS,GAAG,6BAA6B,eAAO,EAAE,CAAC;AACnD,QAAA,cAAc,GAAG,MAAM,CAAC;AACxB,QAAA,yBAAyB,GAAG,2BAA2B,CAAC;AAErE,MAAM,aAAa,GAAuB;IACxC,OAAO,EAAE,sBAAc;IACvB,UAAU,EAAE,yBAAiB;IAC7B,OAAO,EAAE,uBAAa;IACtB,OAAO,EAAE;QACP,YAAY,EAAE,iBAAS;QACvB,iBAAiB,EAAE,sBAAc;KAClC;CACF,CAAC;AAEW,QAAA,oBAAoB,GAAG,eAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAChE,GAAG,CAAC,MAAM,CAAC,4BAAoB,CAAC,CAAC;AAajC;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAa,MAAM;IAIjB,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,KAAoB,EAAE;QACrE,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,mCAC9B,aAAa,CAAC,OAAO,GACrB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CACvC,CAAC;QACJ,CAAC;aAAM,IAAI,MAAM,EAAE,CAAC;YAClB,MAAM,mCAAQ,aAAa,GAAK,MAAM,CAAE,CAAC;YACzC,MAAM,CAAC,OAAO,mCAAQ,aAAa,CAAC,OAAO,GAAK,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,CAAE,CAAC;YACzE,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,4BAAoB,CAAC;QAC5C,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,eAAe,CAAC,GAAG,YAAY,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,eAAe,CAAC,GAAG,GAAa;QAC9B,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,iCAAyB,CAAC;YAC5D,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,iCAAyB,CAAC,CAAC;IACxE,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,UAAU,CAAC,OAA0B;QACnC,OAAO,IAAA,uBAAU,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED,cAAc,CACZ,OAA8B;QAE9B,OAAO,IAAA,yBAAc,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;IAED,SAAS,CAAC,OAAyB;QACjC,OAAO,IAAA,qBAAS,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,QAAQ,CAAC,OAAwB;QAC/B,OAAO,IAAA,mBAAQ,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/C,CAAC;IACD,SAAS,CAAC,OAAyB;QACjC,OAAO,IAAA,qBAAS,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IACD;;;;;;;;;;;;;;;;;;OAkBG;IACH,OAAO,CAAC,OAAuB;QAC7B,OAAO,IAAA,iBAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAED,cAAc,CACZ,OAA8B;QAE9B,OAAO,IAAA,+BAAc,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;IAED,iBAAiB,CACf,OAAiC;QAEjC,OAAO,IAAA,gCAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;IAED,YAAY,CAAC,OAA4B;QACvC,OAAO,IAAA,sBAAY,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IAED,iBAAiB,CACf,OAAiC;QAEjC,OAAO,IAAA,qCAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;IAED,UAAU,CAAC,OAA0B;QACnC,OAAO,IAAA,kBAAU,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED,YAAY,CAAC,OAA4B;QACvC,OAAO,IAAA,2BAAY,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IAED,sBAAsB,CACpB,OAAsC;QAEtC,OAAO,IAAA,0CAAsB,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7D,CAAC;IAED,UAAU,CAAC,OAA0B;QACnC,OAAO,IAAA,uBAAU,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IACD,YAAY,CAAC,OAA4B;QACvC,OAAO,IAAA,2BAAY,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IACD,WAAW,CAAC,OAA2B;QACrC,OAAO,IAAA,yBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAClD,CAAC;CACF;AApID,wBAoIC"}