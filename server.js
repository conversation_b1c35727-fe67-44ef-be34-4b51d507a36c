const express = require('express');
const axios = require('axios');
const { Client } = require('@googlemaps/google-maps-services-js');

const app = express();
const PORT = 3000;

const GOOGLE_API_KEY = 'AIzaSyD4Xh49-JbY5iLLVtcoEnlpxcFOqOLW7p0';  // Replace with your actual API key
const client = new Client({});

// Helper function to convert duration (like "2 hours 30 mins") to minutes
const durationToMinutes = (durationText) => {
    const durationArray = durationText.split(' ');
    let minutes = 0;
    for (let i = 0; i < durationArray.length; i += 2) {
        const value = parseInt(durationArray[i], 10);
        const unit = durationArray[i + 1];
        if (unit.startsWith('hour')) {
            minutes += value * 60;
        } else if (unit.startsWith('min')) {
            minutes += value;
        }
    }
    return minutes;
};

// Helper function to get the latitude and longitude of a location
const getLatLng = async (address) => {
    try {
        const response = await client.geocode({
            params: {
                address: address,
                key: GOOGLE_API_KEY,
            },
            timeout: 1000,
        });

        if (response.data.results[0]) {
            return {
                lat: response.data.results[0].geometry.location.lat,
                lng: response.data.results[0].geometry.location.lng,
            };
        } else {
            return { lat: null, lng: null };
        }
    } catch (error) {
        console.error(`Error fetching lat/lng for ${address}:`, error);
        return { lat: null, lng: null };
    }
};

const getDistanceAndDuration = async (destination) => {
    try {
        const response = await client.distancematrix({
            params: {
                origins: ['Oslo, Norway'],
                destinations: [destination],
                key: GOOGLE_API_KEY,
                mode: 'driving',
            },
            timeout: 1000,
        });

        if (response.data.rows[0].elements[0].status === 'OK') {
            const durationText = response.data.rows[0].elements[0].duration.text;
            return {
                distanceText: response.data.rows[0].elements[0].distance.text,
                distanceValue: response.data.rows[0].elements[0].distance.value,
                durationText: durationText,
                durationValue: durationToMinutes(durationText),
            };
        } else {
            return { distanceText: 'N/A', distanceValue: Infinity, durationText: 'N/A', durationValue: Infinity };
        }
    } catch (error) {
        console.error(`Error fetching distance for ${destination}:`, error);
        return { distanceText: 'N/A', distanceValue: Infinity, durationText: 'N/A', durationValue: Infinity };
    }
};

// Set up body parsing middleware
app.use(express.urlencoded({ extended: true }));

app.get('/', async (req, res) => {
    try {
        const today = new Date().toISOString().split('T')[0];
        const defaultEndDate = new Date();
        defaultEndDate.setDate(defaultEndDate.getDate() + 7);  // Default to one week later
        const defaultEndDateString = defaultEndDate.toISOString().split('T')[0];

        const startDate = req.query.startDate || today;
        const endDate = req.query.endDate || defaultEndDateString;
        const minTime = req.query.minTime ? parseInt(req.query.minTime) : 0;
        const maxTime = req.query.maxTime ? parseInt(req.query.maxTime) : Infinity;

        // Convert dates to timestamps
        const startTimestamp = new Date(startDate).getTime();
        const endTimestamp = new Date(endDate).getTime();

        let baseUrl = `https://www.inatur.no/internal/search?f=[{"felt":"type","sokeord":"hyttetilbud"},{"felt":"antallSenger","fra":1,"til":"*"},{"felt":"amenities","amenities":{"parking":true,"boatAvailable":true,"pet":true}}]&fra=${startTimestamp}&til=${endTimestamp}&ledig=true&p=`;
        let results = [];
        let page = 1;

        // Fetch first page to get total page count and total element count
        let response = await axios.get(baseUrl + page);
        results = results.concat(response.data.resultat);

        let totalPages = response.data.paginering.totaltAntallSider;
        let totalListings = response.data.paginering.totaltAntallElementer;

        // Fetch remaining pages
        for (page = 2; page <= totalPages; page++) {
            response = await axios.get(baseUrl + page);
            results = results.concat(response.data.resultat);
        }

        // Fetch distance, duration, and latitude/longitude for each result
        for (let result of results) {
            const destination = result.kommuner[0] + ', Norway';
            const distanceData = await getDistanceAndDuration(destination);
            const latLng = await getLatLng(destination);
            result.distanceData = distanceData;
            result.latLng = latLng;
        }

        // Sort results by distance
        results.sort((a, b) => a.distanceData.distanceValue - b.distanceData.distanceValue);

        // Filter results based on driving time
        const filteredResults = results.filter(result => {
            const durationValue = result.distanceData.durationValue;
            return durationValue >= minTime * 60 && durationValue <= maxTime * 60;
        });

        // Generate HTML
        let html = `
        <html>
            <head>
                <title>INatur Results</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                    }
                    form {
                        margin-bottom: 20px;
                    }
                    ul {
                        list-style: none;
                        padding: 0;
                    }
                    li {
                        border: 1px solid #ddd;
                        margin: 10px 0;
                        padding: 10px;
                        background: #f9f9f9;
                    }
                    .thumbnail {
                        width: 100px;
                        height: 100px;
                        object-fit: cover;
                    }
                    #map {
                        height: 500px;
                        width: 100%;
                        margin-bottom: 20px;
                    }
                </style>
                <script src="https://maps.googleapis.com/maps/api/js?key=${GOOGLE_API_KEY}&callback=initMap" async defer></script>
                <script>
                    function initMap() {
                        const map = new google.maps.Map(document.getElementById('map'), {
                            zoom: 6,
                            center: { lat: 60.472, lng: 8.4689 },  // Centered on Norway
                        });

                        const locations = ${JSON.stringify(filteredResults.map(result => ({
                            lat: result.latLng.lat,
                            lng: result.latLng.lng,
                            title: result.tittel,
                            url: 'https://www.inatur.no' + result.url,
                            description: result.kortBeskrivelse,
                            price: result.fraPris,
                            distance: result.distanceData.distanceText,
                            travelTime: result.distanceData.durationText
                        })))};

                        locations.forEach(location => {
                            if (location.lat && location.lng) {
                                const marker = new google.maps.Marker({
                                    position: { lat: location.lat, lng: location.lng },
                                    map: map,
                                    title: location.title
                                });

                                const infoWindow = new google.maps.InfoWindow({
                                    content: \`
                                        <div>
                                            <h2><a href="\${location.url}" target="_blank">\${location.title}</a></h2>
                                            <p>\${location.description}</p>
                                            <p><strong>Price:</strong> NOK \${location.price}</p>
                                            <p><strong>Distance from Oslo:</strong> \${location.distance}</p>
                                            <p><strong>Travel time by car:</strong> \${location.travelTime}</p>
                                        </div>
                                    \`
                                });

                                marker.addListener('click', () => {
                                    infoWindow.open(map, marker);
                                });
                            }
                        });
                    }
                </script>
            </head>
            <body>
                <h1>Listings</h1>
                <p>Total Listings: ${filteredResults.length}</p>
                <form method="GET" action="/">
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" value="${startDate}" />
                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" name="endDate" value="${endDate}" />
                    <br/>
                    <label for="minTime">Minimum Travel Time (hours):</label>
                    <input type="number" id="minTime" name="minTime" min="0" step="0.5" value="${minTime || ''}" />
                    <label for="maxTime">Maximum Travel Time (hours):</label>
                    <input type="number" id="maxTime" name="maxTime" min="0" step="0.5" value="${maxTime === Infinity ? '' : maxTime}" />
                    <button type="submit">Filter</button>
                </form>
                <div id="map"></div>
                <ul>`;

        filteredResults.forEach(result => {
            html += `
            <li>
                <img src="${result.thumbnailImageSrc}" alt="${result.tittel}" class="thumbnail">
                <h2><a href="https://www.inatur.no${result.url}">${result.tittel}</a></h2>
                <p>${result.kortBeskrivelse}</p>
                <p><strong>Price:</strong> NOK ${result.fraPris}</p>
                <p><strong>Beds:</strong> ${result.antallSenger}</p>
                <p><strong>Distance from Oslo:</strong> ${result.distanceData.distanceText}</p>
                <p><strong>Travel time by car:</strong> ${result.distanceData.durationText}</p>
                <p><strong>Summer Distance from Road:</strong> ${result.summerDistanceFromRoadInKilometers} km</p>
            </li>`;
        });

        html += `
                </ul>
            </body>
        </html>`;

        res.send(html);

    } catch (error) {
        console.error(error);
        res.status(500).send('An error occurred while fetching data.');
    }
});

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});