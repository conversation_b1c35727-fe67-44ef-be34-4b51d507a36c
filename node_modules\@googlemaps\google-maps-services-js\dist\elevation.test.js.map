{"version": 3, "file": "elevation.test.js", "sourceRoot": "", "sources": ["../src/elevation.test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;AAEH,kDAA0B;AAC1B,2CAA6E;AAE7E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAEnB,MAAM,WAAW,GAAG,eAAkC,CAAC;AAEvD,SAAS,CAAC,GAAG,EAAE;IACb,IAAI,CAAC,aAAa,EAAE,CAAC;AACvB,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,4DAA4D,EAAE,GAAG,EAAE;IACtE,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;IAEpD,IAAA,qBAAS,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,WAAW,CAAC,CAAC;IAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC;QACvC,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,MAAM;QACd,gBAAgB,EAAE,mCAAuB;QACzC,GAAG,EAAE,sBAAU;KAChB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,wDAAwD,EAAE,GAAG,EAAE;IAClE,MAAM,MAAM,GAAG;QACb,IAAI,EAAE;YACJ,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;YACtB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;SACvB;QACD,OAAO,EAAE,EAAE;QACX,GAAG,EAAE,KAAK;KACX,CAAC;IAEF,IAAA,qBAAS,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,WAAW,CAAC,CAAC;IAE3C,MAAM,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,CAAC,WAAW,CAAC,CAAC,oBAAoB,CAAC;QACvC,MAAM,EAAE,KAAK;QACb,MAAM,EAAE,MAAM;QACd,gBAAgB,EAAE,mCAAuB;QACzC,GAAG,EAAE,sBAAU;KAChB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE;IACjD,MAAM,MAAM,GAAG;QACb,IAAI,EAAE;YACJ,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;YACtB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;SACvB;QACD,OAAO,EAAE,EAAE;QACX,GAAG,EAAE,KAAK;KACX,CAAC;IAEF,MAAM,CAAC,IAAA,mCAAuB,EAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAC7C,6CAA6C,CAC9C,CAAC;AACJ,CAAC,CAAC,CAAC"}