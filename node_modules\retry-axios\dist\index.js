var e,t=(e=require("axios"))&&"object"==typeof e&&"default"in e?e.default:e;function r(e){return e}function n(e){var t=[];if(e)return Array.isArray(e)?e:("object"==typeof e&&Object.keys(e).forEach(function(r){"number"==typeof r&&(t[r]=e[r])}),t)}function o(e){if(t.isCancel(e))return Promise.reject(e);var r=f(e)||{};if(r.currentRetryAttempt=r.currentRetryAttempt||0,r.retry="number"==typeof r.retry?r.retry:3,r.retryDelay="number"==typeof r.retryDelay?r.retryDelay:100,r.instance=r.instance||t,r.backoffType=r.backoffType||"exponential",r.httpMethodsToRetry=n(r.httpMethodsToRetry)||["GET","HEAD","PUT","OPTIONS","DELETE"],r.noResponseRetries="number"==typeof r.noResponseRetries?r.noResponseRetries:2,r.checkRetryAfter="boolean"!=typeof r.checkRetryAfter||r.checkRetryAfter,r.maxRetryAfter="number"==typeof r.maxRetryAfter?r.maxRetryAfter:3e5,r.statusCodesToRetry=n(r.statusCodesToRetry)||[[100,199],[429,429],[500,599]],e.config=e.config||{},e.config.raxConfig=Object.assign({},r),!(r.shouldRetry||s)(e))return Promise.reject(e);var o=new Promise(function(t,n){var o=0;if(r.checkRetryAfter&&e.response&&e.response.headers["retry-after"]){var s=function(e){var t=Number(e);if(!Number.isNaN(t))return 1e3*t;var r=Date.parse(e);return Number.isNaN(r)?void 0:r-Date.now()}(e.response.headers["retry-after"]);if(!(s&&s>0&&s<=r.maxRetryAfter))return n(e);o=s}e.config.raxConfig.currentRetryAttempt+=1;var f=e.config.raxConfig.currentRetryAttempt;0===o&&(o="linear"===r.backoffType?1e3*f:"static"===r.backoffType?r.retryDelay:(Math.pow(2,f)-1)/2*1e3,"number"==typeof r.maxRetryDelay&&(o=Math.min(o,r.maxRetryDelay))),setTimeout(t,o)}),i=r.onRetryAttempt?Promise.resolve(r.onRetryAttempt(e)):Promise.resolve();return Promise.resolve().then(function(){return o}).then(function(){return i}).then(function(){return r.instance.request(e.config)})}function s(e){var t=e.config.raxConfig;if(!t||0===t.retry)return!1;if(!e.response&&(t.currentRetryAttempt||0)>=t.noResponseRetries)return!1;if(!e.config.method||t.httpMethodsToRetry.indexOf(e.config.method.toUpperCase())<0)return!1;if(e.response&&e.response.status){for(var r=!1,n=0,o=t.statusCodesToRetry;n<o.length;n+=1){var s=o[n],f=e.response.status;if(f>=s[0]&&f<=s[1]){r=!0;break}}if(!r)return!1}return t.currentRetryAttempt=t.currentRetryAttempt||0,!(t.currentRetryAttempt>=t.retry)}function f(e){if(e&&e.config)return e.config.raxConfig}exports.attach=function(e){return(e=e||t).interceptors.response.use(r,o)},exports.detach=function(e,r){(r=r||t).interceptors.response.eject(e)},exports.shouldRetryRequest=s,exports.getConfig=f;
//# sourceMappingURL=index.js.map
