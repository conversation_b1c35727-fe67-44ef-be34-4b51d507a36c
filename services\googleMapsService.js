/**
 * Google Maps API service for geocoding and distance calculations
 */

const { Client } = require('@googlemaps/google-maps-services-js');
const config = require('../config/config');
const { durationToMinutes, delay, safeGet } = require('../utils/helpers');

class GoogleMapsService {
  constructor() {
    this.client = new Client({});
    this.cache = new Map();
    this.lastRequestTime = 0;
    this.minRequestInterval = 100; // Minimum 100ms between requests
  }

  /**
   * Rate limit API requests to avoid hitting quotas
   */
  async rateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.minRequestInterval) {
      await delay(this.minRequestInterval - timeSinceLastRequest);
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Get latitude and longitude coordinates for an address
   * @param {string} address - The address to geocode
   * @returns {Promise<{lat: number|null, lng: number|null}>} Coordinates or null values
   */
  async getLatLng(address) {
    if (!address || !config.googleMaps.apiKey) {
      return { lat: null, lng: null };
    }

    // Check cache first
    const cacheKey = `geocode_${address}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await this.client.geocode({
        params: {
          address: address,
          key: config.googleMaps.apiKey,
        },
        timeout: config.googleMaps.timeout,
      });

      const result = safeGet(response, 'data.results.0.geometry.location', null);
      
      const coordinates = result ? {
        lat: result.lat,
        lng: result.lng
      } : { lat: null, lng: null };

      // Cache the result
      this.cache.set(cacheKey, {
        data: coordinates,
        timestamp: Date.now()
      });

      return coordinates;

    } catch (error) {
      console.error(`Error geocoding address "${address}":`, error.message);
      return { lat: null, lng: null };
    }
  }

  /**
   * Get distance and duration between origin and destination
   * @param {string} origin - Starting location
   * @param {string} destination - Destination location
   * @returns {Promise<Object>} Distance and duration data
   */
  async getDistanceAndDuration(origin, destination) {
    if (!origin || !destination || !config.googleMaps.apiKey) {
      return this.getDefaultDistanceData();
    }

    // Check cache first
    const cacheKey = `distance_${origin}_${destination}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.app.cacheDuration) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    try {
      await this.rateLimit();

      const response = await this.client.distancematrix({
        params: {
          origins: [origin],
          destinations: [destination],
          key: config.googleMaps.apiKey,
          mode: 'driving',
        },
        timeout: config.googleMaps.timeout,
      });

      const element = safeGet(response, 'data.rows.0.elements.0', null);
      
      if (element && element.status === 'OK') {
        const durationText = safeGet(element, 'duration.text', 'N/A');
        const distanceData = {
          distanceText: safeGet(element, 'distance.text', 'N/A'),
          distanceValue: safeGet(element, 'distance.value', Infinity),
          durationText: durationText,
          durationValue: durationToMinutes(durationText),
        };

        // Cache the result
        this.cache.set(cacheKey, {
          data: distanceData,
          timestamp: Date.now()
        });

        return distanceData;
      } else {
        return this.getDefaultDistanceData();
      }

    } catch (error) {
      console.error(`Error calculating distance from "${origin}" to "${destination}":`, error.message);
      return this.getDefaultDistanceData();
    }
  }

  /**
   * Get default distance data for failed requests
   * @returns {Object} Default distance data
   */
  getDefaultDistanceData() {
    return {
      distanceText: 'N/A',
      distanceValue: Infinity,
      durationText: 'N/A',
      durationValue: Infinity
    };
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}

module.exports = new GoogleMapsService();
