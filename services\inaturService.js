/**
 * Inatur API service for fetching cabin rental data
 */

const axios = require('axios');
const config = require('../config/config');
const { safeGet } = require('../utils/helpers');

class InaturService {
  constructor() {
    this.baseUrl = 'https://www.inatur.no/internal/search';
    this.defaultFilters = [
      { "felt": "type", "sokeord": "hyttetilbud" },
      { "felt": "antallSenger", "fra": 1, "til": "*" },
      { "felt": "amenities", "amenities": { "parking": true, "boatAvailable": true, "pet": true } }
    ];
  }

  /**
   * Build search URL with filters and date range
   * @param {number} startTimestamp - Start date timestamp
   * @param {number} endTimestamp - End date timestamp
   * @param {number} page - Page number
   * @returns {string} Complete search URL
   */
  buildSearchUrl(startTimestamp, endTimestamp, page = 1) {
    const filtersParam = encodeURIComponent(JSON.stringify(this.defaultFilters));
    return `${this.baseUrl}?f=${filtersParam}&fra=${startTimestamp}&til=${endTimestamp}&ledig=true&p=${page}`;
  }

  /**
   * Fetch a single page of results
   * @param {string} url - URL to fetch
   * @returns {Promise<Object>} API response data
   */
  async fetchPage(url) {
    try {
      const response = await axios.get(url, {
        timeout: config.googleMaps.timeout,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      return {
        success: true,
        data: response.data,
        results: safeGet(response.data, 'resultat', []),
        pagination: safeGet(response.data, 'paginering', {})
      };

    } catch (error) {
      console.error('Error fetching page:', error.message);
      return {
        success: false,
        error: error.message,
        results: [],
        pagination: {}
      };
    }
  }

  /**
   * Fetch all available cabin listings for the given date range
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} endDate - End date in YYYY-MM-DD format
   * @returns {Promise<Array>} Array of cabin listings
   */
  async fetchAllListings(startDate, endDate) {
    const startTimestamp = new Date(startDate).getTime();
    const endTimestamp = new Date(endDate).getTime();
    
    let allResults = [];
    let page = 1;
    let totalPages = 1;

    console.log(`Fetching listings from ${startDate} to ${endDate}...`);

    try {
      // Fetch first page to get pagination info
      const firstPageUrl = this.buildSearchUrl(startTimestamp, endTimestamp, page);
      const firstPageResponse = await this.fetchPage(firstPageUrl);

      if (!firstPageResponse.success) {
        throw new Error(`Failed to fetch first page: ${firstPageResponse.error}`);
      }

      allResults = allResults.concat(firstPageResponse.results);
      totalPages = safeGet(firstPageResponse.pagination, 'totaltAntallSider', 1);
      const totalListings = safeGet(firstPageResponse.pagination, 'totaltAntallElementer', 0);

      console.log(`Found ${totalListings} total listings across ${totalPages} pages`);

      // Safety check to prevent excessive API calls
      if (totalPages > config.app.maxPages) {
        console.warn(`Too many pages (${totalPages}), limiting to ${config.app.maxPages}`);
        totalPages = config.app.maxPages;
      }

      // Fetch remaining pages
      const pagePromises = [];
      for (page = 2; page <= totalPages; page++) {
        const pageUrl = this.buildSearchUrl(startTimestamp, endTimestamp, page);
        pagePromises.push(this.fetchPage(pageUrl));
      }

      // Wait for all pages to complete
      const pageResponses = await Promise.allSettled(pagePromises);
      
      pageResponses.forEach((response, index) => {
        if (response.status === 'fulfilled' && response.value.success) {
          allResults = allResults.concat(response.value.results);
        } else {
          console.error(`Failed to fetch page ${index + 2}:`, 
            response.status === 'rejected' ? response.reason : response.value.error);
        }
      });

      console.log(`Successfully fetched ${allResults.length} listings`);
      return allResults;

    } catch (error) {
      console.error('Error fetching listings:', error.message);
      throw error;
    }
  }

  /**
   * Extract location from a listing result
   * @param {Object} listing - Listing object from API
   * @returns {string} Location string for geocoding
   */
  extractLocation(listing) {
    const kommuner = safeGet(listing, 'kommuner', []);
    if (kommuner.length > 0) {
      return `${kommuner[0]}, Norway`;
    }
    return null;
  }

  /**
   * Validate and clean listing data
   * @param {Object} listing - Raw listing from API
   * @returns {Object} Cleaned listing data
   */
  cleanListing(listing) {
    return {
      id: safeGet(listing, 'id', ''),
      title: safeGet(listing, 'tittel', 'Unknown'),
      description: safeGet(listing, 'kortBeskrivelse', ''),
      price: safeGet(listing, 'fraPris', 0),
      beds: safeGet(listing, 'antallSenger', 0),
      url: safeGet(listing, 'url', ''),
      thumbnail: safeGet(listing, 'thumbnailImageSrc', ''),
      location: this.extractLocation(listing),
      summerDistanceFromRoad: safeGet(listing, 'summerDistanceFromRoadInKilometers', 0),
      // Keep original data for backward compatibility
      ...listing
    };
  }
}

module.exports = new InaturService();
