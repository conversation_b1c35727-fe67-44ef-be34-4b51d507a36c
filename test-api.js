/**
 * Simple test script to check if Google Maps API key is working
 */

require('dotenv').config();
const { Client } = require('@googlemaps/google-maps-services-js');

const client = new Client({});
const API_KEY = process.env.GOOGLE_API_KEY;

async function testGoogleMapsAPI() {
  console.log('🧪 Testing Google Maps API...');
  console.log(`📋 API Key: ${API_KEY ? API_KEY.substring(0, 10) + '...' : 'NOT SET'}`);
  
  if (!API_KEY) {
    console.log('❌ No API key found in .env file');
    return;
  }

  try {
    console.log('\n1️⃣ Testing Geocoding API...');
    const geocodeResponse = await client.geocode({
      params: {
        address: 'Oslo, Norway',
        key: API_KEY,
      },
      timeout: 5000,
    });

    if (geocodeResponse.data.results.length > 0) {
      const location = geocodeResponse.data.results[0].geometry.location;
      console.log(`✅ Geocoding works! Oslo coordinates: ${location.lat}, ${location.lng}`);
    } else {
      console.log('❌ Geocoding returned no results');
      return;
    }

    console.log('\n2️⃣ Testing Distance Matrix API...');
    const distanceResponse = await client.distancematrix({
      params: {
        origins: ['Oslo, Norway'],
        destinations: ['Bergen, Norway'],
        key: API_KEY,
        mode: 'driving',
      },
      timeout: 5000,
    });

    const element = distanceResponse.data.rows[0].elements[0];
    if (element.status === 'OK') {
      console.log(`✅ Distance Matrix works! Oslo to Bergen: ${element.distance.text}, ${element.duration.text}`);
      console.log('\n🎉 All tests passed! Your API key is working correctly.');
      console.log('🚀 You can now restart the server and the map functionality should work.');
    } else {
      console.log(`❌ Distance Matrix failed with status: ${element.status}`);
    }

  } catch (error) {
    console.log(`❌ API Error: ${error.message}`);
    
    if (error.message.includes('403')) {
      console.log('\n🔧 403 Error Solutions:');
      console.log('1. Enable billing in Google Cloud Console');
      console.log('2. Enable these APIs: Geocoding API, Distance Matrix API, Maps JavaScript API');
      console.log('3. Remove API key restrictions (for testing)');
      console.log('4. Check your quota limits');
    }
  }
}

// Run the test
testGoogleMapsAPI();
