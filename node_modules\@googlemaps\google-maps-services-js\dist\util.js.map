{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,IAAqB;IAC9C,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,KAAK,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,IAAI,GAAqB,CAAC;IAE1B,MAAM,UAAU,GAAG,UAAU,IAAY;QACvC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;QAC3C,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC9D,IAAI,KAAK,CAAC,CAAC;QACb,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QACjD,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QACrE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;QACrC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;QACrC,KAAK,GAAG,GAAG,CAAC;IACd,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAtBD,gCAsBC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,WAAmB;IAC5C,MAAM,GAAG,GAAW,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;IAC5C,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,KAAK,GAAW,CAAC,CAAC;IACtB,IAAI,GAAG,GAAW,CAAC,CAAC;IACpB,IAAI,GAAG,GAAW,CAAC,CAAC;IACpB,IAAI,UAAkB,CAAC;IAEvB,KAAK,UAAU,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,EAAE,UAAU,EAAE,CAAC;QAC/C,IAAI,MAAM,GAAW,CAAC,CAAC;QACvB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,CAAS,CAAC;QACd,GAAG,CAAC;YACF,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC;YACrB,KAAK,IAAI,CAAC,CAAC;QACb,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;QACpB,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;QAEjD,MAAM,GAAG,CAAC,CAAC;QACX,KAAK,GAAG,CAAC,CAAC;QACV,GAAG,CAAC;YACF,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC;YACrB,KAAK,IAAI,CAAC,CAAC;QACb,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;QACpB,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC;IAC1D,CAAC;IACD,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;IAEzB,OAAO,IAAI,CAAC;AACd,CAAC;AAjCD,gCAiCC"}