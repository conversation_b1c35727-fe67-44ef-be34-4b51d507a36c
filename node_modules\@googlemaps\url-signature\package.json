{"name": "@googlemaps/url-signature", "version": "1.0.36", "description": "Sign a URL for Google Maps Platform requests.", "keywords": ["google", "maps", "polyline"], "homepage": "https://github.com/googlemaps/js-url-signature", "bugs": {"url": "https://github.com/googlemaps/js-url-signature/issues"}, "repository": {"type": "git", "url": "https://github.com/googlemaps/js-url-signature.git"}, "license": "Apache-2.0", "author": "<PERSON>", "main": "dist/index.umd.js", "unpkg": "dist/index.min.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"docs": "typedoc src/index.ts", "format": "eslint . --fix", "lint": "eslint .", "prepare": "rm -rf dist && rollup -c", "test": "jest src/*"}, "dependencies": {"crypto-js": "^4.2.0"}, "devDependencies": {"@babel/preset-env": "^7.15.8", "@rollup/plugin-babel": "^6.0.0", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-typescript": "^11.0.0", "@types/crypto-js": "^4.0.2", "@types/jest": "^29.5.12", "@types/node": "^20.1.0", "@typescript-eslint/eslint-plugin": ">=4.33.0", "@typescript-eslint/parser": ">=4.33.0", "babel": "^6.23.0", "core-js": "^3.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.7.0", "prettier": "^2.1.1", "rollup": "^2.58.3", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.1.4", "typedoc": "^0.26.2", "typescript": "^5.4.5"}, "publishConfig": {"access": "public", "registry": "https://wombat-dressing-room.appspot.com"}}