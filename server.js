/**
 * Inatur Cabin Finder - A web application to search and sort cabin rentals
 * from inatur.no by distance from Oslo with Google Maps integration
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const config = require('./config/config');
const googleMapsService = require('./services/googleMapsService');
const { openStreetMapService } = require('./services/googleMapsService');
const inaturService = require('./services/inaturService');
const { validateDate, validateNumber, safeGet } = require('./utils/helpers');

const app = express();

// Middleware setup
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

/**
 * Main route for searching and displaying cabin listings
 */
app.get('/', async (req, res) => {
  try {
    // Validate and sanitize input parameters
    const today = new Date();
    const defaultEndDate = new Date();
    defaultEndDate.setDate(defaultEndDate.getDate() + config.app.defaultDaysAhead);

    const startDate = validateDate(req.query.startDate, today);
    const endDate = validateDate(req.query.endDate, defaultEndDate);
    const minTime = validateNumber(req.query.minTime, 0, 0, 24);
    const maxTime = validateNumber(req.query.maxTime, Infinity, 0, 24);

    console.log(`Processing request: ${startDate} to ${endDate}, travel time: ${minTime}-${maxTime} hours`);

    // Fetch all listings from Inatur
    const listings = await inaturService.fetchAllListings(startDate, endDate);

    if (listings.length === 0) {
      return res.send(generateNoResultsHtml(startDate, endDate, minTime, maxTime));
    }

    // Enrich listings with distance and location data
    const enrichedListings = await enrichListingsWithLocationData(listings);

    // Sort by distance and filter by travel time
    const sortedListings = enrichedListings
      .sort((a, b) => safeGet(a, 'distanceData.distanceValue', Infinity) - safeGet(b, 'distanceData.distanceValue', Infinity));

    const filteredListings = sortedListings.filter(listing => {
      const durationHours = safeGet(listing, 'distanceData.durationValue', Infinity) / 60;
      return durationHours >= minTime && durationHours <= maxTime;
    });

    // Generate and send HTML response
    const html = generateResultsHtml(filteredListings, startDate, endDate, minTime, maxTime);
    res.send(html);

  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).send(generateErrorHtml(error.message));
  }
});

/**
 * Enrich listings with distance and location data from Google Maps
 * @param {Array} listings - Array of cabin listings
 * @returns {Promise<Array>} Enriched listings with distance and location data
 */
async function enrichListingsWithLocationData(listings) {
  console.log(`Enriching ${listings.length} listings with location data...`);

  const enrichedListings = [];

  for (const [index, listing] of listings.entries()) {
    try {
      const cleanListing = inaturService.cleanListing(listing);
      const location = cleanListing.location;

      if (location) {
        // Get distance and duration data
        const distanceData = await googleMapsService.getDistanceAndDuration(
          config.app.defaultOrigin,
          location
        );

        // Get coordinates for map display
        const coordinates = await googleMapsService.getLatLng(location);

        cleanListing.distanceData = distanceData;
        cleanListing.latLng = coordinates;
      } else {
        // Fallback for listings without location
        cleanListing.distanceData = googleMapsService.getDefaultDistanceData();
        cleanListing.latLng = { lat: null, lng: null };
      }

      enrichedListings.push(cleanListing);

      // Progress logging
      if ((index + 1) % 10 === 0) {
        console.log(`Processed ${index + 1}/${listings.length} listings`);
      }

    } catch (error) {
      console.error(`Error enriching listing ${index}:`, error.message);
      // Add listing without enrichment
      enrichedListings.push(inaturService.cleanListing(listing));
    }
  }

  console.log(`Successfully enriched ${enrichedListings.length} listings`);
  return enrichedListings;
}

/**
 * Generate HTML for successful results
 * @param {Array} listings - Filtered and sorted listings
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @param {number} minTime - Minimum travel time in hours
 * @param {number} maxTime - Maximum travel time in hours
 * @returns {string} Complete HTML page
 */
function generateResultsHtml(listings, startDate, endDate, minTime, maxTime) {
  const mapLocations = listings
    .filter(listing => listing.latLng && listing.latLng.lat && listing.latLng.lng)
    .map(listing => ({
      lat: listing.latLng.lat,
      lng: listing.latLng.lng,
      title: safeGet(listing, 'title', 'Unknown'),
      url: `https://www.inatur.no${safeGet(listing, 'url', '')}`,
      description: safeGet(listing, 'description', ''),
      price: safeGet(listing, 'price', 0),
      distance: safeGet(listing, 'distanceData.distanceText', 'N/A'),
      travelTime: safeGet(listing, 'distanceData.durationText', 'N/A')
    }));

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inatur Cabin Finder - Results</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .filters {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .filters form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        .filters label {
            font-weight: bold;
            color: #34495e;
        }
        .filters input, .filters button {
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }
        .filters button {
            background: #3498db;
            color: white;
            cursor: pointer;
            border: none;
            font-weight: bold;
        }
        .filters button:hover {
            background: #2980b9;
        }
        .stats {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            color: #7f8c8d;
        }
        #map {
            height: 500px;
            width: 100%;
            margin-bottom: 30px;
            border-radius: 5px;
            border: 1px solid #bdc3c7;
        }
        .listings {
            display: grid;
            gap: 20px;
        }
        .listing {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .listing:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .listing img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .listing-content h2 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .listing-content h2 a {
            text-decoration: none;
            color: inherit;
        }
        .listing-content h2 a:hover {
            color: #3498db;
        }
        .listing-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .detail-item {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #3498db;
        }
        .detail-label {
            font-weight: bold;
            color: #34495e;
        }
        .error {
            color: #e74c3c;
            text-align: center;
            padding: 20px;
            background: #fadbd8;
            border-radius: 5px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .listing {
                grid-template-columns: 1fr;
                text-align: center;
            }
            .filters form {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        function initMap() {
            // Initialize OpenStreetMap using Leaflet
            const map = L.map('map').setView([60.472, 8.4689], 6);

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            const locations = ${JSON.stringify(mapLocations)};

            locations.forEach(location => {
                if (location.lat && location.lng) {
                    const marker = L.marker([location.lat, location.lng]).addTo(map);

                    const popupContent = \`
                        <div style="max-width: 300px;">
                            <h3><a href="\${location.url}" target="_blank" style="color: #3498db; text-decoration: none;">\${location.title}</a></h3>
                            <p>\${location.description}</p>
                            <div style="display: grid; gap: 5px; margin-top: 10px;">
                                <div><strong>Price:</strong> NOK \${location.price}</div>
                                <div><strong>Distance:</strong> \${location.distance}</div>
                                <div><strong>Travel time:</strong> \${location.travelTime}</div>
                            </div>
                        </div>
                    \`;

                    marker.bindPopup(popupContent);
                }
            });
        }

        // Initialize map when page loads
        document.addEventListener('DOMContentLoaded', initMap);
    </script>
</head>
<body>
    <div class="container">
        <h1>🏔️ Inatur Cabin Finder</h1>

        <div class="filters">
            <form method="GET" action="/">
                <div>
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" value="${startDate}" required />
                </div>
                <div>
                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" name="endDate" value="${endDate}" required />
                </div>
                <div>
                    <label for="minTime">Min Travel Time (hours):</label>
                    <input type="number" id="minTime" name="minTime" min="0" max="24" step="0.5" value="${minTime || ''}" placeholder="0" />
                </div>
                <div>
                    <label for="maxTime">Max Travel Time (hours):</label>
                    <input type="number" id="maxTime" name="maxTime" min="0" max="24" step="0.5" value="${maxTime === Infinity ? '' : maxTime}" placeholder="No limit" />
                </div>
                <button type="submit">🔍 Search Cabins</button>
            </form>
        </div>

        <div class="stats">
            Found <strong>${listings.length}</strong> cabin${listings.length !== 1 ? 's' : ''} matching your criteria
        </div>

        <div id="map"></div>

        <div class="listings">
            ${listings.map(listing => `
                <div class="listing">
                    <img src="${safeGet(listing, 'thumbnail', '')}" alt="${safeGet(listing, 'title', 'Cabin')}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OSI+Tm8gSW1hZ2U8L3RleHQ+PC9zdmc+'" />
                    <div class="listing-content">
                        <h2><a href="https://www.inatur.no${safeGet(listing, 'url', '')}" target="_blank">${safeGet(listing, 'title', 'Unknown Cabin')}</a></h2>
                        <p>${safeGet(listing, 'description', 'No description available')}</p>
                        <div class="listing-details">
                            <div class="detail-item">
                                <span class="detail-label">Price:</span> NOK ${safeGet(listing, 'price', 0)}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Beds:</span> ${safeGet(listing, 'beds', 0)}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Distance:</span> ${safeGet(listing, 'distanceData.distanceText', 'N/A')}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Travel Time:</span> ${safeGet(listing, 'distanceData.durationText', 'N/A')}
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Distance from Road:</span> ${safeGet(listing, 'summerDistanceFromRoad', 0)} km
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
}

/**
 * Generate HTML for no results found
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @param {number} minTime - Minimum travel time in hours
 * @param {number} maxTime - Maximum travel time in hours
 * @returns {string} HTML page for no results
 */
function generateNoResultsHtml(startDate, endDate, minTime, maxTime) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inatur Cabin Finder - No Results</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        .suggestions {
            text-align: left;
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .suggestions h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .suggestions ul {
            color: #34495e;
        }
        .back-button {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .back-button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 No Cabins Found</h1>
        <div class="message">
            No cabins were found for your search criteria:<br>
            <strong>Dates:</strong> ${startDate} to ${endDate}<br>
            <strong>Travel Time:</strong> ${minTime === 0 ? 'No minimum' : minTime + ' hours'} - ${maxTime === Infinity ? 'No maximum' : maxTime + ' hours'}
        </div>

        <div class="suggestions">
            <h3>Try adjusting your search:</h3>
            <ul>
                <li>Expand your date range</li>
                <li>Increase the maximum travel time</li>
                <li>Remove the minimum travel time restriction</li>
                <li>Try different dates when more cabins might be available</li>
            </ul>
        </div>

        <a href="/" class="back-button">← Back to Search</a>
    </div>
</body>
</html>`;
}

/**
 * Generate HTML for error pages
 * @param {string} errorMessage - Error message to display
 * @returns {string} HTML error page
 */
function generateErrorHtml(errorMessage) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inatur Cabin Finder - Error</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .error-message {
            background: #fadbd8;
            color: #e74c3c;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        .back-button {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .back-button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚠️ Error</h1>
        <div class="error-message">
            ${errorMessage || 'An unexpected error occurred while processing your request.'}
        </div>
        <p>Please try again later or contact support if the problem persists.</p>
        <a href="/" class="back-button">← Back to Search</a>
    </div>
</body>
</html>`;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    googleMapsConfigured: !!config.googleMaps.apiKey
  });
});

// Cache status endpoint (for debugging)
app.get('/cache-stats', (req, res) => {
  res.json({
    openStreetMap: openStreetMapService.getCacheStats(),
    googleMaps: googleMapsService.getCacheStats()
  });
});

// Start the server
const server = app.listen(config.server.port, () => {
  console.log(`🚀 Inatur Cabin Finder server is running!`);
  console.log(`📍 URL: http://localhost:${config.server.port}`);
  console.log(`🗺️  Mapping Service: OpenStreetMap (Nominatim + OSRM - completely free!)`);
  console.log(`🗺️  Google Maps: ${config.googleMaps.apiKey ? 'Available as fallback' : 'Not configured'}`);
  console.log(`⏰ Cache duration: ${config.app.cacheDuration / 1000} seconds`);
  console.log(`🛡️  Rate limit: ${config.rateLimit.max} requests per ${config.rateLimit.windowMs / 60000} minutes`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});